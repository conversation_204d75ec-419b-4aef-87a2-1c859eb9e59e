项目过程中出现的各类异常定义

包括但不限于以下内容：
1、语言基础异常
    空指针
    类型异常：对象错误、索引错误
    数值计算异常：除零、溢出、类型转换错误
2、IO异常
    文件不存在、读写权限不足、磁盘空间不足、文件格式错误等
    网络IO
    序列化/反序列化异常：json解析异常、对象序列化失败
3、数据库相关异常，包括中间件
    数据库异常：连接错误、SQL语法错误、事务处理异常、查询超时等
4、网络服务异常
    网络异常：超时、连接拒绝、网络不可达、DNS解析失败、SSL证书错误、
    外部服务异常：API调用失败、服务不可用、配额超限制（如访问次数到达上限）
5、系统&资源异常
    并发异常：死锁、线程阻塞、线程中断
    资源异常：不足
    操作系统相关异常：进程失败、调用中断等
6、配置异常
    配置项缺失、文件加载失败、配置项格式错误
7、自定义异常
    输入验证异常：表单输入格式不符合要求、必填字段为空、超出范围

