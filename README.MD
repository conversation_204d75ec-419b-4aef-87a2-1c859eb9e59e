LingYuChatBot
介绍
米堆自研产品聆语代码仓库

开发要求说明
1、依赖管理
依赖文件：
    生产依赖：requirements.txt
    开发依赖：requirements-dev.txt
开发时需要保持项目的依赖版本一致，python版本统一使用3.10.11的稳定版
安装指定依赖
pip install -r requirements.txt

若有新的依赖加入，可使用以下命令行记录项目最新依赖
pip freeze > requirements.txt

2、各模块下需要有README.MD文件，文件写明该模块的设计概要
若有设计算法等实现，需表明算法的思路

3、命名规范：
项目中文件函数等命名需直观，可体现功能，不可使用汉语拼音以及拼音缩写，
一律采用英文
变量/函数：snake_case 
类名：CommonCase
常量：ALL_CAPS
需要设计私有变量或者函数的场合，命名以双下划线开头  __private_name
文件以小写字母开头，涉及多个单词中间用大写连接
避免缩写歧义，命名明确性优先
单字母名仅用于循环变量
避免保留字冲突
不可使用否定布尔名称，例如不使用isNotValid，而使用isValid

4、注释与文档：
代码编写过程中需在关键位置必须写有注释；模块、类、公共函数必须写
行内注释：在复杂逻辑前写注释，说明为什么这样做；避免在行尾写注释（除非极短）
类型注解：所有公共接口必须加类型注解；内部函数推荐添加

5、版本控制
分支策略：
    主分支（生产稳定分支，保护分支）：main/xxx 
    开发主分支（保护分支）：develop/xxx
    功能分支：feat/xxx；
    修复分支：fix/xxx；
    发布分支：release/xxx
代码提交规范
    遵循Conventional Commits

6、配置管理
配置信息需实现环境分离：
    开发/测试/生产配置分离
    敏感信息不进代码库（使用环境变量）

7、日志规范
日志级别：
    DEBUG：调试信息
    INFO：关键流程记录
    WARNING：预期内异常
    ERROR：需要干预的错误
结构化日志：
   import logging
   logger = logging.getLogger(__name__)
   def process_data(data):
       try:
           # ...
           logger.info("数据处理成功", extra={"data_id": data.id})
       except Exception:
           logger.exception("处理失败", exc_info=True)
日志统一采用UTF-8编码，日志内容需限，不可无限增加

8、代码编写规范
不写单个行数过长的巨型函数（超过100行）影响阅读，应采用结构化函数

9、错误处理
异常捕获：只捕获具体异常，避免except Exception
        向上抛出异常时保留原始栈信息
自定义异常：业务错误定义明确的异常类型；继承自Exception而非BaseException
资源管理：使用with语句管理资源（文件、锁等）

软件架构
软件架构说明
![img.png](img.png)

安装教程
xxxx
xxxx
xxxx

使用说明
xxxx
xxxx
xxxx

参与贡献
吴可：记忆模块
滕婕：角色模块、MCP模块
李堯霖：DB模块、日志模块

