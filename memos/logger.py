import os
import logging
from typing import Optional


class MemoryOSLogger:
    """MemoryOS 统一日志系统"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, log_file_path: Optional[str] = None):
        if not self._initialized:
            self._setup_logger(log_file_path)
            self._initialized = True
    
    def _setup_logger(self, log_file_path: Optional[str] = None):
        """设置日志系统"""
        if log_file_path is None:
            log_file_path = "/root/wuke/LingYuChatBot/data/memory/memoryos.log"
        
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file_path)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        
        # 配置根日志记录器
        self.logger = logging.getLogger('MemoryOS')
        self.logger.setLevel(logging.INFO)
        self.logger.handlers.clear()  # 清除现有处理器
        self.logger.addHandler(file_handler)
        
        # 禁用控制台输出
        self.logger.propagate = False
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取指定名称的日志记录器
        
        Args:
            name: 日志记录器名称
            
        Returns:
            logging.Logger: 日志记录器实例
        """
        logger = logging.getLogger(f'MemoryOS.{name}')
        logger.handlers.clear()  # 清除现有处理器
        logger.propagate = False  # 禁用传播到父记录器
        
        # 获取根记录器的处理器
        root_logger = logging.getLogger('MemoryOS')
        for handler in root_logger.handlers:
            logger.addHandler(handler)
        
        return logger


def get_logger(name: str, log_file_path: Optional[str] = None) -> logging.Logger:
    """
    获取日志记录器的便捷函数
    
    Args:
        name: 日志记录器名称
        log_file_path: 日志文件路径（可选）
        
    Returns:
        logging.Logger: 日志记录器实例
    """
    logger_instance = MemoryOSLogger(log_file_path)
    return logger_instance.get_logger(name)


def setup_logger(log_file_path: Optional[str] = None) -> logging.Logger:
    """
    设置并返回主日志记录器（向后兼容）
    
    Args:
        log_file_path: 日志文件路径（可选）
        
    Returns:
        logging.Logger: 主日志记录器实例
    """
    return get_logger('', log_file_path) 