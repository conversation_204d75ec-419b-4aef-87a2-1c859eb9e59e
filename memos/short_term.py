import json
from collections import deque

from .utils import get_timestamp, ensure_directory_exists
from .logger import get_logger


class ShortTermMemory:
    def __init__(self, file_path, max_capacity=10):
        self.max_capacity = max_capacity
        self.file_path = file_path
        ensure_directory_exists(self.file_path)
        self.memory = deque(maxlen=max_capacity)
        
        # 设置日志记录器
        self.logger = get_logger('ShortTermMemory')
        
        self.load()

    def add_qa_pair(self, qa_pair):
        # Ensure timestamp exists, add if not
        if 'timestamp' not in qa_pair or not qa_pair['timestamp']:
            qa_pair["timestamp"] = get_timestamp()
        
        self.memory.append(qa_pair)
        self.logger.info(f"短期记忆: 已添加问答对。用户输入: {qa_pair.get('user_input','')[:30]}...")
        self.save()

    def get_all(self):
        return list(self.memory)

    def is_full(self):
        return len(self.memory) >= self.max_capacity # Use >= to be safe

    def pop_oldest(self):
        if self.memory:
            msg = self.memory.popleft()
            self.logger.info("短期记忆: 已移除最旧的问答对。")
            self.save()
            return msg
        return None

    def save(self):
        try:
            with open(self.file_path, "w", encoding="utf-8") as f:
                json.dump(list(self.memory), f, ensure_ascii=False, indent=2)
        except IOError as e:
            self.logger.error(f"保存短期记忆到 {self.file_path} 时出错: {e}")

    def load(self):
        try:
            with open(self.file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                # Ensure items are loaded correctly, especially if file was empty or malformed
                if isinstance(data, list):
                    self.memory = deque(data, maxlen=self.max_capacity)
                else:
                    self.memory = deque(maxlen=self.max_capacity)
            self.logger.info(f"短期记忆: 已从 {self.file_path} 加载。")
        except FileNotFoundError:
            self.memory = deque(maxlen=self.max_capacity)
            self.logger.info(f"短期记忆: 在 {self.file_path} 未找到历史文件。正在初始化新记忆。")
        except json.JSONDecodeError:
            self.memory = deque(maxlen=self.max_capacity)
            self.logger.error(f"短期记忆: 从 {self.file_path} 解码JSON时出错。正在初始化新记忆。")
        except Exception as e:
            self.memory = deque(maxlen=self.max_capacity)
            self.logger.error(f"短期记忆: 从 {self.file_path} 加载时发生意外错误: {e}。正在初始化新记忆。") 