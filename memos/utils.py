# 此文件已被重构，模型调用相关功能已移动到 models/ 目录
# 为了向后兼容，保留基础工具函数

import time
import uuid
import numpy as np
import json
import os
import inspect
from functools import wraps
import requests
import re

# 导入日志记录器
try:
    from .logger import get_logger
except ImportError:
    from logger import get_logger

# 创建日志记录器
logger = get_logger('Utils')

def clean_reasoning_model_output(text):
    """
    清理推理模型输出中的<think>标签
    适配推理模型（如o1系列）的输出格式
    """
    if not text:
        return text
    
    # 移除<think>...</think>标签及其内容
    cleaned_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    # 清理可能产生的多余空白行
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
    # 移除开头和结尾的空白
    cleaned_text = cleaned_text.strip()
    
    return cleaned_text

# ---- Parallel Processing Utilities ----
def run_parallel_tasks(tasks, max_workers=3):
    """
    并行执行任务列表
    tasks: List of callable functions
    """
    from concurrent.futures import ThreadPoolExecutor, as_completed
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(task) for task in tasks]
        results = []
        for future in as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                logger.error(f"Error in parallel task: {e}")
                results.append(None)
        return results

# ---- Basic Utilities ----
def get_timestamp():
    return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

def generate_id(prefix="id"):
    return f"{prefix}_{uuid.uuid4().hex[:8]}"

def ensure_directory_exists(path):
    os.makedirs(os.path.dirname(path), exist_ok=True)

# ---- Embedding Utilities ----
_embedding_cache = {}  # 添加embedding缓存

def _get_valid_kwargs(func, kwargs):
    """Helper to filter kwargs for a given function's signature."""
    try:
        sig = inspect.signature(func)
        param_keys = set(sig.parameters.keys())
        return {k: v for k, v in kwargs.items() if k in param_keys}
    except (ValueError, TypeError):
        # Fallback for functions/methods where signature inspection is not straightforward
        return kwargs

# def get_embedding(text, model_name="BAAI/bge-large-zh-v1.5", use_cache=True, api_token="sk-qxehwrwohkxucyroiqhqtuuqmeewjxwigpbppbmyjqsyoxne", **kwargs):
#     """
#     获取文本的embedding向量。
#     通过硅基流动API调用，支持多种主流模型。
#     可用模型：
#     - BAAI/bge-large-zh-v1.5
#     - BAAI/bge-large-en-v1.5
#     - netease-youdao/bce-embedding-base_v1
#     - BAAI/bge-m3
#     - Pro/BAAI/bge-m3
#     - Qwen/Qwen3-Embedding-8B
#     - Qwen/Qwen3-Embedding-4B
#     - Qwen/Qwen3-Embedding-0.6B

#     :param text: 输入文本。
#     :param model_name: 硅基流动支持的模型名称。
#     :param use_cache: 是否使用内存缓存。
#     :param api_token: 硅基流动API的访问令牌。
#     :param kwargs: 其他参数（保留兼容性）。
#     :return: 文本的embedding向量 (numpy array)。
#     """
#     if not api_token:
#         raise ValueError("必须提供硅基流动API的访问令牌 (api_token)")
    
#     # 构建缓存键
#     cache_key = f"{model_name}::{hash(text)}"
    
#     if use_cache and cache_key in _embedding_cache:
#         return _embedding_cache[cache_key]
    
#     # 调用硅基流动API
#     url = "https://api.siliconflow.cn/v1/embeddings"
#     payload = {
#         "model": model_name,
#         "input": text
#     }
#     headers = {
#         "Authorization": f"Bearer {api_token}",
#         "Content-Type": "application/json"
#     }
    
#     try:
#         logger.info(f"调用硅基流动API获取embedding，模型: {model_name}")
#         response = requests.post(url, json=payload, headers=headers)
#         response.raise_for_status()  # 检查HTTP错误
        
#         result = response.json()
        
#         # 提取embedding向量
#         if "data" in result and len(result["data"]) > 0:
#             embedding = result["data"][0]["embedding"]
#             embedding = np.array(embedding, dtype=np.float32)
#         else:
#             raise ValueError(f"API响应格式错误: {result}")
        
#         # 缓存结果
#         if use_cache:
#             _embedding_cache[cache_key] = embedding
#             if len(_embedding_cache) > 10000:
#                 keys_to_remove = list(_embedding_cache.keys())[:1000]
#                 for key in keys_to_remove:
#                     try:
#                         del _embedding_cache[key]
#                     except KeyError:
#                         pass
#                 logger.info("清理embedding缓存以防止内存溢出")
        
#         return embedding
        
#     except requests.exceptions.RequestException as e:
#         logger.error(f"硅基流动API调用失败: {e}")
#         raise
#     except (KeyError, ValueError) as e:
#         logger.error(f"解析API响应失败: {e}")
#         raise
#     except Exception as e:
#         logger.error(f"获取embedding时发生未知错误: {e}")
#         raise

def clear_embedding_cache():
    """清空embedding缓存"""
    global _embedding_cache
    _embedding_cache.clear()
    logger.info("Embedding cache cleared")

def normalize_vector(vec):
    vec = np.array(vec, dtype=np.float32)
    norm = np.linalg.norm(vec)
    if norm == 0:
        return vec
    return vec / norm

# ---- Time Decay Function ----
def compute_time_decay(event_timestamp_str, current_timestamp_str, tau_hours=24):
    from datetime import datetime
    fmt = "%Y-%m-%d %H:%M:%S"
    try:
        t_event = datetime.strptime(event_timestamp_str, fmt)
        t_current = datetime.strptime(current_timestamp_str, fmt)
        delta_hours = (t_current - t_event).total_seconds() / 3600.0
        return np.exp(-delta_hours / tau_hours)
    except ValueError: # Handle cases where timestamp might be invalid
        return 0.1 # Default low recency

# 兼容性类，保持向后兼容
# class OpenAIClient:
#     """
#     兼容性类，保持向后兼容
#     """
#     def __init__(self, api_key, base_url=None, max_workers=5):
#         import sys
#         import os
#         sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
#         from models.model_controller import ModelController
        
#         config = {
#             'openai_api_key': api_key,
#             'openai_base_url': base_url,
#             'max_workers': max_workers
#         }
#         self.controller = ModelController(config)
    
#     def chat_completion(self, model, messages, temperature=0.7, max_tokens=2000):
#         return self.controller.chat_completion(model, messages, temperature, max_tokens)
    
#     def chat_completion_async(self, model, messages, temperature=0.7, max_tokens=2000):
#         return self.controller.chat_completion_async(model, messages, temperature, max_tokens)
    
#     def batch_chat_completion(self, requests):
#         return self.controller.batch_chat_completion(requests)
    
#     def shutdown(self):
#         self.controller.shutdown() 