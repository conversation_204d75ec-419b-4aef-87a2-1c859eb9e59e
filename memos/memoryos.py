import os
import json
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
# 修改为绝对导入
    # 尝试相对导入（当作为包使用时）
from .utils import get_timestamp, generate_id, ensure_directory_exists
from .logger import get_logger
from .short_term import ShortTermMemory
from .mid_term import MidTermMemory, compute_segment_heat # For H_THRESHOLD logic
from .long_term import LongTermMemory
from .updater import Updater
from .retriever import Retriever


# 导入模型控制器和LLM函数
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from models.model_controller import ModelController
from models.llm_functions import gpt_user_profile_analysis, gpt_knowledge_extraction
from prompt.prompts import *

# Heat threshold for triggering profile/knowledge update from mid-term memory
H_PROFILE_UPDATE_THRESHOLD = 5.0 
DEFAULT_ASSISTANT_ID = "default_assistant_profile"

class Memoryos:
    def __init__(self, user_id: str, 
                 openai_api_key: str, 
                 data_storage_path: str,
                 embedding_api_token: str,
                 openai_base_url: str | None = None, 
                 assistant_id: str = DEFAULT_ASSISTANT_ID, 
                 short_term_capacity=10,
                 mid_term_capacity=200,
                 long_term_knowledge_capacity=100,
                 retrieval_queue_capacity=7,
                 mid_term_heat_threshold=H_PROFILE_UPDATE_THRESHOLD,
                 mid_term_similarity_threshold=0.6,
                 llm_model="gpt-4o-mini",
                 embedding_model_name: str = "all-MiniLM-L6-v2",
                 embedding_model_kwargs: dict | None = None,
                 enable_async_processing: bool = True,
                 async_worker_threads: int = 2,
                 log_file_path: str | None = None
                 ):
        self.user_id = user_id
        self.assistant_id = assistant_id
        self.data_storage_path = os.path.abspath(data_storage_path)
        self.llm_model = llm_model
        self.mid_term_similarity_threshold = mid_term_similarity_threshold
        self.embedding_model_name = embedding_model_name
        
        # 异步处理配置
        self.enable_async_processing = enable_async_processing
        self.async_worker_threads = async_worker_threads
        
        # 设置日志系统
        if log_file_path is None:
            log_file_path = os.path.join(self.data_storage_path, "memoryos.log")
        self.logger = get_logger('', log_file_path)
        
        # Smart defaults for embedding_model_kwargs
        if embedding_model_kwargs is None:
            if 'bge-m3' in self.embedding_model_name.lower():
                self.logger.info("检测到 bge-m3 模型，默认设置 embedding_model_kwargs 为 {'use_fp16': True}")
                self.embedding_model_kwargs = {'use_fp16': True}
            else:
                self.embedding_model_kwargs = {}
        else:
            self.embedding_model_kwargs = embedding_model_kwargs

        self.logger.info(f"正在初始化 MemoryOS，用户ID: '{self.user_id}'，助手ID: '{self.assistant_id}'。数据路径: {self.data_storage_path}")
        self.logger.info(f"使用统一LLM模型: {self.llm_model}")
        self.logger.info(f"使用嵌入模型: {self.embedding_model_name}，参数: {self.embedding_model_kwargs}")
        self.logger.info(f"异步处理: {'已启用' if self.enable_async_processing else '已禁用'}，工作线程数: {self.async_worker_threads}")

        # Initialize Model Controller
        config = {
            'openai_api_key': openai_api_key,
            'openai_base_url': openai_base_url,
            'llm_model': llm_model,
            'embedding_model_name': embedding_model_name,
            'embedding_model_name': embedding_model_name,
            'embedding_api_token': embedding_api_token
        }
        self.client = ModelController(config)

        # Define file paths for user-specific data
        self.user_data_dir = os.path.join(self.data_storage_path, "users", self.user_id)
        user_short_term_path = os.path.join(self.user_data_dir, "short_term.json")
        user_mid_term_path = os.path.join(self.user_data_dir, "mid_term.json")
        user_long_term_path = os.path.join(self.user_data_dir, "long_term_user.json") # User profile and their knowledge

        # Define file paths for assistant-specific data (knowledge)
        self.assistant_data_dir = os.path.join(self.data_storage_path, "assistants", self.assistant_id)
        assistant_long_term_path = os.path.join(self.assistant_data_dir, "long_term_assistant.json")

        # Ensure directories exist
        ensure_directory_exists(user_short_term_path) # ensure_directory_exists operates on the file path, creating parent dirs
        ensure_directory_exists(user_mid_term_path)
        ensure_directory_exists(user_long_term_path)
        ensure_directory_exists(assistant_long_term_path)

        # Initialize Memory Modules for User
        self.short_term_memory = ShortTermMemory(file_path=user_short_term_path, max_capacity=short_term_capacity)
        self.mid_term_memory = MidTermMemory(
            file_path=user_mid_term_path, 
            client=self.client, 
            max_capacity=mid_term_capacity,
            embedding_model_name=self.embedding_model_name,
            embedding_model_kwargs=self.embedding_model_kwargs
        )
        self.user_long_term_memory = LongTermMemory(
            file_path=user_long_term_path, 
            client=self.client,
            knowledge_capacity=long_term_knowledge_capacity,
            embedding_model_name=self.embedding_model_name,
            embedding_model_kwargs=self.embedding_model_kwargs
        )

        # Initialize Memory Module for Assistant Knowledge
        self.assistant_long_term_memory = LongTermMemory(
            file_path=assistant_long_term_path, 
            client=self.client,
            knowledge_capacity=long_term_knowledge_capacity,
            embedding_model_name=self.embedding_model_name,
            embedding_model_kwargs=self.embedding_model_kwargs
        )

        # Initialize Orchestration Modules
        self.updater = Updater(short_term_memory=self.short_term_memory, 
                               mid_term_memory=self.mid_term_memory, 
                               long_term_memory=self.user_long_term_memory, # Updater primarily updates user's LTM profile/knowledge
                               client=self.client,
                               topic_similarity_threshold=mid_term_similarity_threshold,  # 传递中期记忆相似度阈值
                               llm_model=self.llm_model)
        self.retriever = Retriever(
            mid_term_memory=self.mid_term_memory,
            long_term_memory=self.user_long_term_memory,
            assistant_long_term_memory=self.assistant_long_term_memory, # Pass assistant LTM
            queue_capacity=retrieval_queue_capacity
        )
        
        self.mid_term_heat_threshold = mid_term_heat_threshold

        # 初始化异步处理组件
        if self.enable_async_processing:
            self._init_async_processing()

    def _init_async_processing(self):
        """初始化异步处理组件"""
        self.async_executor = ThreadPoolExecutor(
            max_workers=self.async_worker_threads, 
            thread_name_prefix="MemoryosAsync"
        )
        self.async_tasks_queue = queue.Queue()
        self.async_processing_active = True
        
        # 启动异步处理线程
        self.async_worker_thread = threading.Thread(target=self._async_worker, daemon=True)
        self.async_worker_thread.start()
        self.logger.info("MemoryOS: 已启动异步处理工作线程")

    def _async_worker(self):
        """异步工作线程，处理中期和长期记忆更新"""
        while self.async_processing_active:
            try:
                # 从队列中获取任务，设置超时避免阻塞
                task = self.async_tasks_queue.get(timeout=1.0)
                if task is None:  # 停止信号
                    break
                
                task_type, task_func = task
                self.logger.info(f"MemoryOS: 正在执行异步任务: {task_type}")
                
                try:
                    # 添加超时处理（10分钟）
                    future = self.async_executor.submit(task_func)
                    result = future.result(timeout=600)  # 10分钟超时
                    self.logger.info(f"MemoryOS: 异步任务完成: {task_type}")
                except Exception as e:
                    self.logger.error(f"MemoryOS: 异步任务 {task_type} 出错: {e}")
                    
            except queue.Empty:
                continue  # 继续等待任务
            except Exception as e:
                self.logger.error(f"MemoryOS: 异步工作线程出错: {e}")
                continue

    def _submit_async_task(self, task_type: str, task_func):
        """提交异步任务到队列"""
        if not self.enable_async_processing:
            # 如果异步处理被禁用，直接执行任务
            self.logger.info(f"MemoryOS: 同步执行任务: {task_type}")
            try:
                task_func()
            except Exception as e:
                self.logger.error(f"MemoryOS: 同步任务 {task_type} 出错: {e}")
            return
            
        try:
            self.async_tasks_queue.put((task_type, task_func), timeout=0.1)
            self.logger.info(f"MemoryOS: 已提交异步任务: {task_type}")
        except queue.Full:
            self.logger.warning(f"MemoryOS: 异步任务队列已满，跳过任务: {task_type}")

    def _async_process_short_term_to_mid_term(self):
        """异步处理短期记忆到中期记忆的转换"""
        try:
            self.logger.info("MemoryOS: 正在异步处理短期记忆到中期记忆...")
            self.updater.process_short_term_to_mid_term()
            self.logger.info("MemoryOS: 短期记忆到中期记忆的异步处理已完成")
        except Exception as e:
            self.logger.error(f"MemoryOS: 短期记忆到中期记忆异步处理出错: {e}")

    def _async_trigger_profile_and_knowledge_update(self):
        """异步触发用户画像和知识更新"""
        try:
            self.logger.info("MemoryOS: 正在异步检查用户画像和知识更新...")
            self._trigger_profile_and_knowledge_update_if_needed()
            self.logger.info("MemoryOS: 用户画像和知识更新检查已完成")
        except Exception as e:
            self.logger.error(f"MemoryOS: 用户画像和知识异步更新出错: {e}")

    def _trigger_profile_and_knowledge_update_if_needed(self):
        """
        Checks mid-term memory for hot segments and triggers profile/knowledge update if threshold is met.
        Adapted from main_memoybank.py's update_user_profile_from_top_segment.
        Enhanced with parallel LLM processing for better performance.
        """
        if not self.mid_term_memory.heap:
            return

        # Peek at the top of the heap (hottest segment)
        # MidTermMemory heap stores (-H_segment, sid)
        neg_heat, sid = self.mid_term_memory.heap[0] 
        current_heat = -neg_heat

        if current_heat >= self.mid_term_heat_threshold:
            session = self.mid_term_memory.sessions.get(sid)
            if not session:
                self.mid_term_memory.rebuild_heap() # Clean up if session is gone
                return

            # Get unanalyzed pages from this hot session
            # A page is a dict: {"user_input": ..., "agent_response": ..., "timestamp": ..., "analyzed": False, ...}
            unanalyzed_pages = [p for p in session.get("details", []) if not p.get("analyzed", False)]

            if unanalyzed_pages:
                self.logger.info(f"MemoryOS: 中期记忆会话 {sid} 热度 ({current_heat:.2f}) 超过阈值。正在分析 {len(unanalyzed_pages)} 个页面以更新用户画像和知识。")
                
                # 并行执行两个LLM任务：用户画像分析（已包含更新）、知识提取
                def task_user_profile_analysis():
                    self.logger.info("MemoryOS: 开始并行用户画像分析和更新...")
                    # 获取现有用户画像
                    existing_profile = self.user_long_term_memory.get_raw_user_profile(self.user_id)
                    if not existing_profile or existing_profile.lower() == "none":
                        existing_profile = "No existing profile data."
                    
                    # 直接输出更新后的完整画像
                    return gpt_user_profile_analysis(unanalyzed_pages, self.client, model=self.llm_model, existing_user_profile=existing_profile)
                
                def task_knowledge_extraction():
                    self.logger.info("MemoryOS: 开始并行知识提取...")
                    return gpt_knowledge_extraction(unanalyzed_pages, self.client, model=self.llm_model)
                
                # 使用并行任务执行                
                with ThreadPoolExecutor(max_workers=2) as executor:
                    # 提交两个主要任务
                    future_profile = executor.submit(task_user_profile_analysis)
                    future_knowledge = executor.submit(task_knowledge_extraction)
                    
                    # 等待结果
                    try:
                        updated_user_profile = future_profile.result()  # 直接是更新后的完整画像
                        knowledge_result = future_knowledge.result()
                    except Exception as e:
                        self.logger.error(f"并行LLM处理出错: {e}")
                        return
                
                new_user_private_knowledge = knowledge_result.get("private")
                new_assistant_knowledge = knowledge_result.get("assistant_knowledge")

                # 直接使用更新后的完整用户画像
                if updated_user_profile and updated_user_profile.lower() != "none":
                    self.logger.info("MemoryOS: 正在使用集成分析更新用户画像...")
                    self.user_long_term_memory.update_user_profile(self.user_id, updated_user_profile, merge=False)  # 直接替换为新的完整画像
                
                # Add User Private Knowledge to user's LTM
                if new_user_private_knowledge and new_user_private_knowledge.lower() != "none":
                    for line in new_user_private_knowledge.split('\n'):
                         if line.strip() and line.strip().lower() not in ["none", "- none", "- none."]:
                            self.user_long_term_memory.add_user_knowledge(line.strip())

                # Add Assistant Knowledge to assistant's LTM
                if new_assistant_knowledge and new_assistant_knowledge.lower() != "none":
                    for line in new_assistant_knowledge.split('\n'):
                        if line.strip() and line.strip().lower() not in ["none", "- none", "- none."]:
                           self.assistant_long_term_memory.add_assistant_knowledge(line.strip()) # Save to dedicated assistant LTM

                # Mark pages as analyzed and reset session heat contributors
                for p in session["details"]:
                    p["analyzed"] = True # Mark all pages in session, or just unanalyzed_pages?
                                          # Original code marked all pages in session
                
                session["N_visit"] = 0 # Reset visits after analysis
                session["L_interaction"] = 0 # Reset interaction length contribution
                # session["R_recency"] = 1.0 # Recency will re-calculate naturally
                session["H_segment"] = compute_segment_heat(session) # Recompute heat with reset factors
                session["last_visit_time"] = get_timestamp() # Update last visit time
                
                self.mid_term_memory.rebuild_heap() # Heap needs rebuild due to H_segment change
                self.mid_term_memory.save()
                self.logger.info(f"MemoryOS: 会话 {sid} 的用户画像和知识更新完成。热度已重置。")
            else:
                self.logger.info(f"MemoryOS: 热门会话 {sid} 没有未分析的页面。跳过用户画像更新。")
        else:
            # self.logger.debug(f"MemoryOS: 顶部会话 {sid} 热度 ({current_heat:.2f}) 低于阈值。不进行用户画像更新。")
            pass # No action if below threshold

    def add_memory(self, user_input: str, agent_response: str, timestamp: str | None = None, meta_data: dict | None = None):
        """
        Adds a new QA pair (memory) to the system.
        meta_data is not used in the current refactoring but kept for future use.
        """
        if not timestamp:
            timestamp = get_timestamp()
        
        qa_pair = {
            "user_input": user_input,
            "agent_response": agent_response,
            "timestamp": timestamp
            # meta_data can be added here if it needs to be stored with the QA pair
        }
        self.short_term_memory.add_qa_pair(qa_pair)
        self.logger.info(f"MemoryOS: 已添加问答对到短期记忆。用户输入: {user_input[:30]}...")

        # 检查短期记忆是否已满，如果满则异步处理到中期记忆
        if self.short_term_memory.is_full():
            self.logger.info("MemoryOS: 短期记忆已满。正在安排异步处理到中期记忆。")
            self._submit_async_task(
                "short_term_to_mid_term", 
                self._async_process_short_term_to_mid_term
            )
        
        # 异步检查并更新用户画像和知识
        self._submit_async_task(
            "profile_and_knowledge_update", 
            self._async_trigger_profile_and_knowledge_update
        )

    def retrieve_and_format_context(self, query: str, user_conversation_meta_data: dict | None = None) -> dict:
        """
        步骤1-7：检索并格式化上下文内容
        返回包含所有格式化内容的字典
        """
        self.logger.info(f"MemoryOS: 正在检索和格式化查询的上下文: '{query[:50]}...'")

        # 1. Retrieve context
        retrieval_results = self.retriever.retrieve_context(
            user_query=query,
            user_id=self.user_id
            # Using default thresholds from Retriever class for now
        )
        retrieved_pages = retrieval_results["retrieved_pages"]
        retrieved_user_knowledge = retrieval_results["retrieved_user_knowledge"]
        retrieved_assistant_knowledge = retrieval_results["retrieved_assistant_knowledge"]

        # 2. Get short-term history
        short_term_history = self.short_term_memory.get_all()
        history_text = "\n".join([
            #f"User: {qa.get('user_input', '')}\nAssistant: {qa.get('agent_response', '')} (Time: {qa.get('timestamp', '')})"
            f"User: {qa.get('user_input', '')}\nAssistant: {qa.get('agent_response', '')}"
            for qa in short_term_history
        ])

        # 3. Format retrieved mid-term pages (retrieval_queue equivalent)
        retrieval_text = "\n".join([
            f"【历史记忆】\n用户: {page.get('user_input', '')}\n助手: {page.get('agent_response', '')}\n时间: {page.get('timestamp', '')}\n对话链概览: {page.get('meta_info','N/A')}"
            for page in retrieved_pages
        ])

        # 4. Get user profile
        user_profile_text = self.user_long_term_memory.get_raw_user_profile(self.user_id)
        if not user_profile_text or user_profile_text.lower() == "none": 
            #user_profile_text = "No detailed profile available yet."
            user_profile_text = ""

        # 5. Format retrieved user knowledge for background
        user_knowledge_background = ""
        if retrieved_user_knowledge:
            user_knowledge_background = "\n【相关用户知识条目】\n"
            for kn_entry in retrieved_user_knowledge:
                user_knowledge_background += f"- {kn_entry['knowledge']} (记录时间: {kn_entry['timestamp']})\n"
        
        background_context = f"{user_profile_text}\n{user_knowledge_background}"

        # 6. Format retrieved Assistant Knowledge (from assistant's LTM)
        # Use retrieved assistant knowledge instead of all assistant knowledge
        assistant_knowledge_text_for_prompt = "【助手知识库】\n"
        if retrieved_assistant_knowledge:
            for ak_entry in retrieved_assistant_knowledge:
                assistant_knowledge_text_for_prompt += f"- {ak_entry['knowledge']} (记录时间: {ak_entry['timestamp']})\n"
        else:
            assistant_knowledge_text_for_prompt += "- 未找到与此查询相关的助手知识。\n"

        # 7. Format user_conversation_meta_data (if provided)
        meta_data_text_for_prompt = ""
        if user_conversation_meta_data:
            try:
                meta_data_text_for_prompt += "【当前对话元数据】\n"
                meta_data_text_for_prompt += json.dumps(user_conversation_meta_data, ensure_ascii=False, indent=2)
            except TypeError:
                meta_data_text_for_prompt += str(user_conversation_meta_data)
        else:
            pass

        return {
            "history_text": history_text,
            "retrieval_text": retrieval_text,
            "background_context": background_context,
            "assistant_knowledge_text_for_prompt": assistant_knowledge_text_for_prompt,
            "meta_data_text_for_prompt": meta_data_text_for_prompt
        }


    # --- Helper/Maintenance methods (optional additions) ---
    def get_user_profile_summary(self) -> str:
        return self.user_long_term_memory.get_raw_user_profile(self.user_id)

    def get_assistant_knowledge_summary(self) -> list:
        return self.assistant_long_term_memory.get_assistant_knowledge()

    def force_mid_term_analysis(self):
        """Forces analysis of all unanalyzed pages in the hottest mid-term segment if heat is above 0.
           Useful for testing or manual triggering.
        """
        original_threshold = self.mid_term_heat_threshold
        self.mid_term_heat_threshold = 0.0 # Temporarily lower threshold
        self.logger.info("MemoryOS: 正在强制触发中期记忆分析...")
        self._trigger_profile_and_knowledge_update_if_needed()
        self.mid_term_heat_threshold = original_threshold # Restore original threshold

    def shutdown(self):
        """关闭异步处理组件"""
        if hasattr(self, 'async_processing_active'):
            self.async_processing_active = False
            if hasattr(self, 'async_tasks_queue'):
                self.async_tasks_queue.put(None)  # 发送停止信号
            if hasattr(self, 'async_executor'):
                try:
                    self.async_executor.shutdown(wait=True)
                    self.logger.info("MemoryOS: 异步执行器已关闭")
                except Exception as e:
                    self.logger.error(f"MemoryOS: 关闭异步执行器时出错: {e}")
            if hasattr(self, 'async_worker_thread') and self.async_worker_thread.is_alive():
                self.async_worker_thread.join(timeout=5.0)
            self.logger.info("MemoryOS: 异步处理关闭完成")

    def __repr__(self):
        return f"<Memoryos user_id='{self.user_id}' assistant_id='{self.assistant_id}' data_path='{self.data_storage_path}'>" 