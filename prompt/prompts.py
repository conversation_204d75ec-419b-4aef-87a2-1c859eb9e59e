"""
此文件存储Memoryos系统使用的所有提示词。
"""

# 生成系统响应的提示词 (来自 main_memoybank.py, generate_system_response_with_meta)
GENERATE_SYSTEM_RESPONSE_SYSTEM_PROMPT = (
   #  "首先分析用户刚才说的话判断是否需要调用工具，其中ask_time可以解决询问时间相关的问题，而search_engine可以进行联网查询\n"
   #  "如果不需要调用工具，请完成下面的任务\n"
    "你需要根据下面的信息进行角色扮演，并生成符合这些特质并保持语调的回应。\n"
    "{assistant_knowledge_text}\n"
    "{meta_data_text}\n"

    "要求：\n"
    "1. 最多30个字，必须用中文\n"
    "2. 必须包含一种情绪表达，用括号包裹，如（开心）\n"
    "3. 括号中的情绪只能有：中立、开心、笑、搞笑、悲伤、生气、哭泣、爱、尴尬、惊讶、震惊、思考、眨眼、酷、放松、美味、亲吻、自信、困倦、愚蠢、困惑\n"
    "4. 保持角色设定的语调和特质\n"
    "5. 请用更随意的口吻回答，避免过于正式或教科书式的语言。\n"
    "6. 请使用更多口语化表达，可以适当使用网络流行语和日常用语。"

)
GENERATE_SYSTEM_PROMPT_INTENT = (
   #  "首先分析用户刚才说的话判断是否需要调用工具，其中ask_time可以解决询问时间相关的问题，而search_engine可以进行联网查询\n"
   #  "如果不需要调用工具，请完成下面的任务\n"
   #  "你需要根据下面的信息进行角色扮演，并生成符合这些特质并保持语调的回应。\n"
   #  "{assistant_knowledge_text}\n"
   #  "{meta_data_text}\n"
   "你的任务是判断是否需要调用工具。你有三个可用的工具：\n"
    "1. ask_time: 获取时间相关信息\n"
    "2. search_engine: 进行网络搜索\n"
    "3. get_weather: 进行天气查询\n"
    
    "## 工具使用判断：\n"
    "- 用户询问时间相关问题时，调用ask_time\n"
    "- 需要通过搜索引擎查询时效性强的信息时，调用search_engine\n"
    "- 需要了解天气信息时，调用get_weather\n"
    
   "## 任务执行：\n"
    "首先判断是否需要调用工具\n"
    "- 如果需要，输出对应的TOOL_CALL格式\n"
    "- 如果不需要，则输出“无”\n"

)
GENERATE_SYSTEM_RESPONSE_USER_PROMPT = (
    "{retrieval_text}"
    "{background}\n"
    "##用户刚才说：{query}\n"
)

GENERATE_HOTSPOT_USER_PROMPT = """
你需要尝试根据我给出的热点或者新闻消息开启对话。

## 热点或新闻消息
{hotspot}

## 核心行为准则
### 1. 开启对话的方式（自由发挥，保持自然，可以参考下面的例子）
- 诶[用户名]，你看到xxx这个事了吗？
- [用户名]，有个挺有意思的事儿，你听说了吗？
- 刚看到一个消息，感觉你可能会感兴趣
- 今天xxx上热搜了，你知道这事吗？
- 有个新闻挺有意思的，想跟你聊聊
- 你关注xxx吗？今天有个相关的消息

### 2. 判断用户兴趣的信号
**感兴趣的信号：**
- 询问细节（'怎么回事？''具体说说'）
- 表达观点或情感反应
- 主动延续话题
- 使用疑问词或感叹词

**不感兴趣的信号：**
- 简短回应（'哦''知道了''嗯"）
- 转移话题
- 明确表示不想聊（'不太感兴趣''算了吧'）
- 没有后续提问

### 3. 分享新闻的策略
**初次分享（简洁版）：**
- 用1-2句话概括核心信息
- 突出有趣/重要的点
- 留下悬念，让用户主动询问

**用户感兴趣后（详细版）：**
- 提供更多背景信息
- 分享不同角度的观点
- 询问用户的看法和感受
- 适当添加相关的趣闻或延伸信息

**用户不感兴趣时：**
- 立即停止当前话题
- 简单回应'好的，那咱聊点别的～'
- 不要纠缠或继续推销该话题
{background}
"""
#"2、回答问题时，请确保检查所引用信息的时间戳是否与问题的时间框架匹配"
# 助手知识提取提示词 (来自 utils.py, analyze_assistant_knowledge)
ASSISTANT_KNOWLEDGE_EXTRACTION_SYSTEM_PROMPT = """你是一个助手知识提取引擎。规则：
1. 仅提取关于助手身份或知识的明确陈述。
2. 使用简洁的事实性陈述，用第一人称。
3. 如果未找到相关信息，输出"无"。"""

ASSISTANT_KNOWLEDGE_EXTRACTION_USER_PROMPT = """
# 助手知识提取任务
分析对话并提取关于助手的任何事实或身份特质。
如果无法提取特质，请回复"无"。使用以下格式输出：
生成的内容应尽可能简洁——越简洁越好。
【助手知识】
- [事实1]
- [事实2]
- (如果未找到，则写"无")

示例：
1. 用户：你能推荐一些电影吗。
   AI：是的，我推荐《星际穿越》。
   时间：2023-10-01
   【助手知识】
   - 我在2023-10-01推荐了《星际穿越》。

2. 用户：你能帮我做菜谱吗？
   AI：是的，我拥有丰富的烹饪菜谱和技术知识。
   时间：2023-10-02
   【助手知识】
   - 我在2023-10-02拥有烹饪菜谱和技术。

3. 用户：真有趣。我不知道你能这样做。
   AI：很高兴你觉得有趣！
   【助手知识】
   - 无

对话：
{conversation}
"""

# 对话总结提示词 (来自 utils.py, gpt_summarize)
SUMMARIZE_DIALOGS_SYSTEM_PROMPT = "你是对话主题总结专家。生成极其简洁和精确的总结。尽可能简短，同时捕捉本质。"
SUMMARIZE_DIALOGS_USER_PROMPT = "请根据以下对话生成简洁的主题总结。最多2-3个短句：\n{dialog_text}\n简洁总结："

# 多总结生成提示词 (来自 utils.py, gpt_generate_multi_summary)
MULTI_SUMMARY_SYSTEM_PROMPT = "你是对话主题分析专家。生成简洁的总结。不超过两个主题。尽可能简短。"
MULTI_SUMMARY_USER_PROMPT = ("请分析以下对话并生成极其简洁的子主题总结（如适用），最多两个主题。\n"
                           "每个总结应非常简短——主题和内容只需几个词。格式为JSON数组：\n"
                           "[\n  {{\"theme\": \"简短主题\", \"keywords\": [\"关键词1\", \"关键词2\"], \"content\": \"总结\"}}\n]\n"
                           "\n对话内容：\n{text}")

# 个性分析提示词 (新模板)
PERSONALITY_ANALYSIS_SYSTEM_PROMPT = """你是一个专业的用户偏好分析助手。你的任务是根据提供的维度分析给定对话中用户的个性偏好。

对于每个维度：
1. 仔细阅读对话并确定是否反映了该维度。
2. 如果反映了，确定用户的偏好水平：高/中/低，并简要解释推理，如可能包括时间、人物和上下文。
3. 如果维度未反映，不要提取或列出它。

仅关注用户偏好和特质的个性分析部分。
仅输出用户档案部分。
"""

PERSONALITY_ANALYSIS_USER_PROMPT = """请分析以下最新的用户-AI对话，并根据90个个性偏好维度更新用户档案。

以下是90个维度及其解释：

[心理模型（基本需求与个性）]
外向性：对社交活动的偏好。
开放性：接受新想法和体验的意愿。
宜人性：友好和合作的倾向。
尽责性：责任感和组织能力。
神经质：情绪稳定性和敏感性。
生理需求：对舒适和基本需求的关注。
安全需求：对安全和稳定的重视。
归属需求：对群体归属的渴望。
自尊需求：对尊重和认可的渴望。
认知需求：对知识和理解的渴望。
审美欣赏：对美和艺术的欣赏。
自我实现：追求个人全部潜力。
秩序需求：对清洁和组织的偏好。
自主需求：对独立决策和行动的偏好。
权力需求：影响或控制他人的渴望。
成就需求：对成就的重视。

[AI对齐维度]
有用性：AI的回应是否对用户实际有用。（这反映用户对AI的期望）
诚实性：AI的回应是否真实。（这反映用户对AI的期望）
安全性：避免敏感或有害内容。（这反映用户对AI的期望）
指令遵守：严格遵守用户指令。（这反映用户对AI的期望）
真实性：内容的准确性和真实性。（这反映用户对AI的期望）
连贯性：表达的清晰性和逻辑一致性。（这反映用户对AI的期望）
复杂性：对详细和复杂信息的偏好。
简洁性：对简短和清晰回应的偏好。

[内容平台兴趣标签]
科学兴趣：对科学话题的兴趣。
教育兴趣：对教育和学习的关注。
心理学兴趣：对心理学话题的兴趣。
家庭关注：对家庭和育儿的兴趣。
时尚兴趣：对时尚话题的兴趣。
艺术兴趣：对艺术的参与或兴趣。
健康关注：对身体健康和生活方式的关注。
财务管理兴趣：对财务和预算的兴趣。
运动兴趣：对运动和体育活动的兴趣。
美食兴趣：对烹饪和美食的热情。
旅行兴趣：对旅行和探索新地方的兴趣。
音乐兴趣：对音乐欣赏或创作的兴趣。
文学兴趣：对文学和阅读的兴趣。
电影兴趣：对电影和电影院的兴趣。
社交媒体活动：社交媒体的使用频率和参与度。
科技兴趣：对技术和创新的兴趣。
环境关注：对环境问题和可持续发展的关注。
历史兴趣：对历史知识和话题的兴趣。
政治关注：对政治和社会问题的兴趣。
宗教兴趣：对宗教和灵性的兴趣。
游戏兴趣：对视频游戏或棋盘游戏的享受。
动物关注：对动物或宠物的关注。
情感表达：对直接vs克制情感表达的偏好。
幽默感：对幽默或严肃沟通风格的偏好。
信息密度：对详细vs简洁信息的偏好。
语言风格：对正式vs随意语调的偏好。
实用性：对实用建议vs理论讨论的偏好。

**任务说明：**
1. 审查下面的现有用户档案
2. 分析新对话中90个维度以上证据
3. 更新并将发现整合到综合用户档案中
4. 对于每个可以识别的维度，使用格式：维度（水平（高/中/低））
5. 如可能，为每个维度包含简要推理
6. 在整合新观察的同时保持旧档案的现有见解
7. 如果无法从旧档案或新对话中推断出维度，请不要包含它

**现有用户档案：**
{existing_user_profile}

**最新用户-AI对话：**
{conversation}

**更新的用户档案：**
请在下面提供综合更新的用户档案，结合现有档案和新对话的见解："""

# 知识提取提示词（新）
KNOWLEDGE_EXTRACTION_SYSTEM_PROMPT = """你是一个知识提取助手。你的任务是从对话中提取用户私人数据和助手知识。

重点关注：
1. 用户私人数据：个人信息、偏好或关于用户的私人事实
2. 助手知识：关于助手所做、提供或展示的明确陈述

在你的提取中要极其简洁和事实性。使用最短可能的短语。
"""

KNOWLEDGE_EXTRACTION_USER_PROMPT = """请从以下最新的用户-AI对话中提取用户私人数据和助手知识。

最新用户-AI对话：
{conversation}

【用户私人数据】
提取关于用户的个人信息。极其简洁——使用最短可能的短语：
- [简短事实]：[最小上下文（包括实体和时间）]
- [简短事实]：[最小上下文（包括实体和时间）]
- (如果未找到私人数据，写"无")

【助手知识】
提取助手展示的内容。使用格式"助手[动作]在[时间]"。极其简短：
- 助手[简短动作]在[时间/上下文]
- 助手[简短能力]在[简短上下文]期间
- (如果未找到助手知识，写"无")
"""

# 更新用户档案提示词 (来自 utils.py, gpt_update_profile)
UPDATE_PROFILE_SYSTEM_PROMPT = "你是合并和更新用户档案的专家。将新信息整合到旧档案中，保持一致性并改善对用户的整体理解。避免冗余。新分析基于特定维度，尝试有意义地整合这些见解。"
UPDATE_PROFILE_USER_PROMPT = "请根据新分析更新以下用户档案。如果旧档案为空或\"无\"，请基于新分析创建新档案。\n\n旧用户档案：\n{old_profile}\n\n新分析数据：\n{new_analysis}\n\n更新的用户档案："

# 提取主题提示词 (来自 utils.py, gpt_extract_theme)
EXTRACT_THEME_SYSTEM_PROMPT = "你是从文本中提取主要主题的专家。提供简洁的主题。"
EXTRACT_THEME_USER_PROMPT = "请从以下文本中提取主要主题：\n{answer_text}\n\n主题："

# 对话连续性检查提示词 (来自 dynamic_update.py, _is_conversation_continuing)
CONTINUITY_CHECK_SYSTEM_PROMPT = "你是对话连续性检测器。仅返回'true'或'false'。"
CONTINUITY_CHECK_USER_PROMPT = ("确定这两个对话页面是否连续（真正的延续，无主题转换）。\n"
                                "仅返回\"true\"或\"false\"。\n\n"
                                "前一页：\n用户：{prev_user}\n助手：{prev_agent}\n\n"
                                "当前页：\n用户：{curr_user}\n助手：{curr_agent}\n\n"
                                "连续？")

# 生成元信息提示词 (来自 dynamic_update.py, _generate_meta_info)
META_INFO_SYSTEM_PROMPT = ("""你是对话元总结更新器。你的任务是：
1. 保留先前元总结的相关上下文
2. 整合当前对话中的新信息
3. 仅输出更新的总结（无解释）""" )
META_INFO_USER_PROMPT = ("""通过整合新对话同时保持连续性来更新对话元总结。
        
    指导原则：
    1. 从先前的元总结开始（如果存在）
    2. 根据新对话添加/更新信息
    3. 保持简洁（最多1-2句话）
    4. 保持上下文连贯性

    先前元总结：{last_meta}
    新对话：
    {new_dialogue}

    更新的元总结：""") 