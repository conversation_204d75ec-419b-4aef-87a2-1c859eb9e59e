import random
from typing import Dict, List, Any
from copy import deepcopy


class CharacterGeneratorCore:
    """角色生成的核心逻辑"""

    def __init__(self, base_template: Dict[str, Any],
                 character_variations: Dict[str, Any],
                 character_types_manager):
        self.base_template = base_template
        self.character_variations = character_variations
        self.types_manager = character_types_manager

    def generate_character(self, character_type: str = "random",
                           custom_traits: Dict[str, Dict[str, int]] = None) -> Dict[str, Any]:
        """生成角色"""
        if not self.base_template or not self.character_variations:
            print("无法生成角色：缺少必要的数据文件")
            return {}

        new_character = deepcopy(self.base_template)

        if custom_traits:
            self._apply_custom_traits(new_character, custom_traits)
        else:
            self._apply_type_traits(new_character, character_type)

        return new_character

    def _apply_custom_traits(self, character: Dict[str, Any],
                             custom_traits: Dict[str, Dict[str, int]]):
        """应用自定义特质"""
        for dimension, traits in custom_traits.items():
            if dimension in self.character_variations:
                for trait, index in traits.items():
                    if trait in self.character_variations[dimension]:
                        variations = self.character_variations[dimension][trait]
                        if 0 <= index < len(variations):
                            character[dimension][trait] = variations[index]

    def _apply_type_traits(self, character: Dict[str, Any], character_type: str):
        """根据类型应用特质"""
        if character_type not in self.types_manager.type_mapping:
            print(f"警告：未知的角色类型 '{character_type}'，将使用随机生成")
            character_type = "random"

        trait_index = self.types_manager.type_mapping.get(character_type, 0)

        for dimension, traits in self.character_variations.items():
            for trait, variations in traits.items():
                if character_type == "random" or trait_index == -1:
                    index = random.randint(0, len(variations) - 1)
                else:
                    index = min(trait_index, len(variations) - 1)
                character[dimension][trait] = variations[index]

    def generate_archetype(self, archetype: str) -> Dict[str, Any]:
        """根据原型生成角色"""
        index = self.types_manager.get_archetype_index(archetype)
        target_type = self.types_manager.find_type_by_index(index)

        if target_type is None:
            print(f"警告：无法找到索引为 {index} 的角色类型，使用第一个可用类型")
            target_type = list(self.types_manager.type_mapping.keys())[
                0] if self.types_manager.type_mapping else "random"

        return self.generate_character(target_type)

    def blend_characters(self, char1: Dict[str, Any], char2: Dict[str, Any],
                         ratio: float = 0.5) -> Dict[str, Any]:
        """融合两个角色"""
        blended = deepcopy(char1)
        for dimension in blended:
            for trait in blended[dimension]:
                if random.random() < ratio:
                    blended[dimension][trait] = char2[dimension][trait]
        return blended

    def mutate_character(self, character: Dict[str, Any],
                         mutation_rate: float = 0.2) -> Dict[str, Any]:
        """随机变异角色"""
        mutated = deepcopy(character)
        for dimension, traits in self.character_variations.items():
            for trait_name, variations in traits.items():
                if random.random() < mutation_rate:
                    index = random.randint(0, len(variations) - 1)
                    mutated[dimension][trait_name] = variations[index]
        return mutated

    def analyze_traits(self, character: Dict[str, Any]) -> Dict[str, int]:
        """分析角色特征倾向"""
        counts = {"关爱型": 0, "分析型": 0, "创意型": 0, "传统型": 0, "进取型": 0}
        trait_names = list(counts.keys())

        for dimension, traits in character.items():
            if dimension in self.character_variations:
                for trait_name, trait_value in traits.items():
                    if trait_name in self.character_variations[dimension]:
                        variations = self.character_variations[dimension][trait_name]
                        try:
                            index = variations.index(trait_value)
                            counts[trait_names[index]] += 1
                        except ValueError:
                            continue
        return counts

    def print_summary(self, character: Dict[str, Any]):
        """打印角色摘要"""
        print("\n" + "=" * 50)
        print("角色摘要")
        print("=" * 50)
        for dimension, traits in character.items():
            print(f"\n【{dimension}】")
            for trait_name, trait_value in traits.items():
                print(f"  {trait_name}: {trait_value[:50]}...")