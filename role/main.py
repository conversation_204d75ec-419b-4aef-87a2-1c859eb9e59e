#!/usr/bin/env python3
"""
角色生成器主程序入口
"""

from character_generator import CharacterGenerator


def main():
    """主函数"""
    # 创建生成器实例
    generator = CharacterGenerator()

    # 启动交互式界面
    generator.interactive_generate()


def demo_usage():
    '''程序化使用方式：直接在代码中调用函数，不需要用户交互使用，适合集成到其它系统中，适合开发者使用'''
    print("=== 演示程序化使用 ===")

    # 创建生成器
    generator = CharacterGenerator()

    # 列出可用类型
    generator.list_types()

    # 生成一个创意型角色
    print("\n生成创意型角色:")
    creative_char = generator.generate_character("creative")
    generator.print_summary(creative_char)

    # 分析特征
    traits = generator.analyze_traits(creative_char)
    print(f"\n特征分析: {traits}")

    # 生成一个治愈师原型
    print("\n生成治愈师原型:")
    healer_char = generator.generate_archetype("治愈师")
    generator.print_summary(healer_char)

    # 融合两个角色
    print("\n融合两个角色:")
    blended_char = generator.blend_characters(creative_char, healer_char, 0.6)
    generator.print_summary(blended_char)


if __name__ == "__main__":
    try:
        # 运行主程序:'''交互式使用方式：用户通过菜单选择、输入数字来操作，程序会显示选项，等待用户输入，适合普通用户使用'''
        main()

        # 如果需要演示程序化使用，取消下面的注释
        # demo_usage()

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")