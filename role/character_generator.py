from typing import Dict, Any
from file_loader import FileLoader
from character_types import CharacterTypes
from character_generator_core import CharacterGeneratorCore
from ui_manager import UIManager


class CharacterGenerator:
    """整合的角色生成器主类"""

    def __init__(self, template_file: str = "./characters/woman/general.json",
                 variations_file: str = "./characters/woman/morecharacters.json",
                 types_file: str = "./characters/woman/character_type.json"):
        """初始化角色生成器"""
        self.template_file = template_file
        self.variations_file = variations_file
        self.types_file = types_file

        # 加载数据
        self.base_template = FileLoader.load_json(template_file, "模板文件")
        self.character_variations = FileLoader.load_json(variations_file, "变化数据文件")
        character_types_data = FileLoader.load_json(types_file, "类型文件")

        # 初始化管理器
        self.types_manager = CharacterTypes(character_types_data)
        self.generator_core = CharacterGeneratorCore(
            self.base_template,
            self.character_variations,
            self.types_manager
        )
        self.ui_manager = UIManager(self.types_manager)

    def generate_character(self, character_type: str = "random",
                           custom_traits: Dict[str, Dict[str, int]] = None) -> Dict[str, Any]:
        """生成角色"""
        return self.generator_core.generate_character(character_type, custom_traits)

    def generate_archetype(self, archetype: str) -> Dict[str, Any]:
        """根据原型生成角色"""
        return self.generator_core.generate_archetype(archetype)

    def blend_characters(self, char1: Dict[str, Any], char2: Dict[str, Any],
                         ratio: float = 0.5) -> Dict[str, Any]:
        """融合两个角色"""
        return self.generator_core.blend_characters(char1, char2, ratio)

    def mutate_character(self, character: Dict[str, Any],
                         mutation_rate: float = 0.2) -> Dict[str, Any]:
        """随机变异角色"""
        return self.generator_core.mutate_character(character, mutation_rate)

    def analyze_traits(self, character: Dict[str, Any]) -> Dict[str, int]:
        """分析角色特征倾向"""
        return self.generator_core.analyze_traits(character)

    def save_character(self, character: Dict[str, Any], filename: str):
        """保存角色到文件"""
        FileLoader.save_json(character, filename, "角色")

    def print_summary(self, character: Dict[str, Any]):
        """打印角色摘要"""
        self.generator_core.print_summary(character)

    def interactive_generate(self):
        """交互式生成角色"""
        self.ui_manager.show_welcome_message()

        while True:
            self.ui_manager.display_main_menu()

            try:
                mode = input("\n请选择模式 (1/2/3): ").strip()

                if mode == "1":
                    # 按性格特征生成
                    self._generate_by_personality()

                elif mode == "2":
                    # 按职业角色生成
                    self._generate_by_archetype()

                elif mode == "3":
                    self.ui_manager.show_goodbye_message()
                    break

                else:
                    print("请输入有效的选项 (1/2/3)")

            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见!")
                break
            except Exception as e:
                print(f"发生错误: {e}")

    def _generate_by_personality(self):
        """按性格特征生成角色"""
        type_list = self.ui_manager.display_types_menu()
        selected_type = self.ui_manager.get_user_choice(
            type_list, "请选择你想要的性格特征"
        )

        if selected_type:
            self.ui_manager.show_generation_message(selected_type)
            character = self.generate_character(selected_type)

            if character:
                self.print_summary(character)
                traits = self.analyze_traits(character)
                self.ui_manager.show_traits_analysis(traits)
                self._handle_save_character(character)

    def _generate_by_archetype(self):
        """按职业角色生成角色"""
        archetype_list = self.ui_manager.display_archetypes_menu()
        selected_archetype = self.ui_manager.get_user_choice(
            archetype_list, "请选择你想要的职业角色"
        )

        if selected_archetype:
            self.ui_manager.show_generation_message(selected_archetype, is_archetype=True)
            character = self.generate_archetype(selected_archetype)

            if character:
                self.print_summary(character)
                traits = self.analyze_traits(character)
                self.ui_manager.show_traits_analysis(traits)
                self._handle_save_character(character)

    def _handle_save_character(self, character: Dict[str, Any]):
        """处理保存角色"""
        should_save, filename = self.ui_manager.ask_save_character()
        if should_save:
            self.save_character(character, filename)

    def list_types(self):
        """列出所有可用类型"""
        self.types_manager.list_types()

    def list_archetypes(self):
        """列出所有可用原型"""
        self.types_manager.list_archetypes()

    def get_type_description(self, type_key: str) -> str:
        """获取类型描述"""
        return self.types_manager.get_type_description(type_key)

    def get_type_value(self, type_key: str) -> int:
        """获取类型值"""
        return self.types_manager.get_type_value(type_key)