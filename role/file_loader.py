import json
import os
from typing import Dict, Any


class FileLoader:
    """负责加载和处理JSON配置文件"""

    @staticmethod
    def load_json(file_path: str, description: str = "文件") -> Dict[str, Any]:
        """加载JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"{description} {file_path} 不存在")
            return {}
        except json.JSONDecodeError:
            print(f"{description} {file_path} 格式错误")
            return {}

    @staticmethod
    def save_json(data: Dict[str, Any], file_path: str, description: str = "文件"):
        """保存JSON文件到指定目录"""
        # 设置输出目录
        output_dir = "./characters_output"

        # 如果目录不存在，创建它
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"创建输出目录: {output_dir}")

        # 构建完整的文件路径
        full_path = os.path.join(output_dir, file_path)

        try:
            with open(full_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"{description}已保存到 {full_path}")
        except Exception as e:
            print(f"保存{description}时发生错误: {e}")