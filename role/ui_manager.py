from typing import List, Dict, Any


class UIManager:
    """处理用户界面和交互"""

    def __init__(self, character_types_manager):
        self.types_manager = character_types_manager

    def display_types_menu(self) -> List[str]:
        """显示性格特征菜单"""
        print("\n" + "=" * 70)
        print("🎭 性格特征选择菜单")
        print("💡 提示：每种特征代表不同的性格倾向和行为方式")
        print("=" * 70)

        type_list = []
        index = 0

        for type_key, type_data in self.types_manager.character_types.items():
            if type_key.lower() == "random":
                continue

            if isinstance(type_data, dict):
                description = type_data.get("description", "无描述")
                print(f"  {index}. {type_key} - {description}")
            else:
                print(f"  {index}. {type_key} - {type_data}")
            type_list.append(type_key)
            index += 1

        print(f"  {index}. random - 🎲 完全随机生成，混合所有特征")
        type_list.append("random")

        return type_list

    def display_archetypes_menu(self) -> List[str]:
        """显示职业角色菜单"""
        print("\n" + "=" * 70)
        print("🏛️ 职业角色选择菜单")
        print("💡 提示：每种职业代表不同的社会角色和专业领域")
        print("=" * 70)

        archetype_list = list(self.types_manager.archetypes.keys())
        descriptions = {
            "治愈师": "关怀他人、提供帮助的角色",
            "学者": "专注学术研究、知识渊博的角色",
            "艺术家": "富有创造力、追求美感的角色",
            "守护者": "保护他人、责任感强的角色",
            "领袖": "具有领导力、善于决策的角色"
        }

        for index, archetype in enumerate(archetype_list):
            desc = descriptions.get(archetype, "")
            print(f"  {index}. {archetype} - {desc}")

        return archetype_list

    def get_user_choice(self, options_list: List[str], prompt: str = "请选择") -> str:
        """获取用户选择"""
        while True:
            try:
                choice = input(f"\n{prompt} (输入数字，回车确认): ").strip()

                if choice == "":
                    print(f"使用默认选项: {options_list[0]}")
                    return options_list[0]

                choice_index = int(choice)

                if 0 <= choice_index < len(options_list):
                    selected_option = options_list[choice_index]
                    print(f"您选择了: {selected_option}")
                    return selected_option
                else:
                    print(f"请输入 0 到 {len(options_list) - 1} 之间的数字")

            except ValueError:
                print("请输入有效的数字")
            except KeyboardInterrupt:
                print("\n程序被用户中断")
                return None

    def display_main_menu(self):
        """显示主菜单"""
        print("\n" + "=" * 70)
        print("🎯 请选择你想要的角色生成方式:")
        print("  1. 按性格特征生成 (关爱型、分析型、创意型等)")
        print("  2. 按职业角色生成 (治愈师、学者、艺术家等)")
        print("  3. 退出程序")

    def ask_save_character(self) -> tuple:
        """询问是否保存角色"""
        save_choice = input("\n💾 是否保存角色到文件? (y/n): ").strip().lower()
        if save_choice == 'y':
            filename = input("请输入文件名 (默认: character.json): ").strip()
            if not filename:
                filename = "character.json"
            if not filename.endswith('.json'):
                filename += '.json'
            return True, filename
        return False, None

    def show_welcome_message(self):
        """显示欢迎信息"""
        print("\n🎮 欢迎使用交互式角色生成器!")

    def show_goodbye_message(self):
        """显示告别信息"""
        print("\n👋 感谢使用角色生成器，再见!")

    def show_generation_message(self, type_or_archetype: str, is_archetype: bool = False):
        """显示生成消息"""
        if is_archetype:
            print(f"\n🎲 正在生成 '{type_or_archetype}' 职业的角色...")
        else:
            print(f"\n🎲 正在生成具有 '{type_or_archetype}' 性格特征的角色...")

    def show_traits_analysis(self, traits: Dict[str, int]):
        """显示特征分析"""
        print(f"\n📊 性格特征统计: {traits}")