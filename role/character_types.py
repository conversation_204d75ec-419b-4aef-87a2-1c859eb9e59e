from typing import Dict, Any


class CharacterTypes:
    """管理角色类型和原型的映射"""

    def __init__(self, character_types: Dict[str, Any]):
        self.character_types = character_types
        self.type_mapping = self._build_type_mapping()

        # 预定义的角色原型
        self.archetypes = {
            "治愈师": 0, "学者": 1, "艺术家": 2, "守护者": 3, "领袖": 4
        }

    def _build_type_mapping(self) -> Dict[str, int]:
        """从JSON格式构建类型映射"""
        if not self.character_types:
            print("警告：无法加载角色类型文件，类型映射为空")
            return {}

        type_mapping = {}
        for type_key, type_data in self.character_types.items():
            if isinstance(type_data, dict) and "value" in type_data:
                type_mapping[type_key] = type_data["value"]
            else:
                print(f"警告：类型 '{type_key}' 使用旧格式，请更新为新格式")
                type_mapping[type_key] = len(type_mapping)

        return type_mapping

    def get_type_description(self, type_key: str) -> str:
        """获取类型描述"""
        if type_key in self.character_types:
            type_data = self.character_types[type_key]
            if isinstance(type_data, dict):
                return type_data.get("description", "无描述")
            else:
                return str(type_data)
        return "未知类型"

    def get_type_value(self, type_key: str) -> int:
        """获取类型值"""
        return self.type_mapping.get(type_key, 0)

    def list_types(self):
        """列出所有可用类型"""
        if not self.character_types:
            print("无法加载角色类型信息")
            return

        print("\n可用的角色类型:")
        for type_key, type_data in self.character_types.items():
            if isinstance(type_data, dict):
                description = type_data.get("description", "无描述")
                value = type_data.get("value", 0)
                print(f"  {type_key} (值: {value}): {description}")
            else:
                print(f"  {type_key}: {type_data}")

    def list_archetypes(self):
        """列出所有可用原型"""
        print("\n可用的角色原型:")
        for archetype in self.archetypes:
            print(f"  {archetype}")

    def get_archetype_index(self, archetype: str) -> int:
        """获取原型对应的索引"""
        if archetype not in self.archetypes:
            raise ValueError(f"未知的角色原型: {archetype}")
        return self.archetypes[archetype]

    def find_type_by_index(self, index: int) -> str:
        """根据索引找到对应的类型"""
        for type_key, type_value in self.type_mapping.items():
            if type_value == index:
                return type_key
        return list(self.type_mapping.keys())[0] if self.type_mapping else "random"