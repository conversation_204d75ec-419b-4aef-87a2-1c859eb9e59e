# 🎭 角色生成器 (Character Generator)

一个功能强大的角色生成工具，支持基于性格特征和职业原型的角色创建，提供交互式界面和程序化API。

## ✨ 主要特性

- 🎯 **多种生成方式**：支持按性格特征或职业原型生成角色
- 🎲 **智能随机化**：基于配置文件的智能随机角色生成
- 🔄 **角色操作**：支持角色融合、变异等高级操作
- 📊 **特征分析**：自动分析角色的性格特征倾向
- 💾 **数据持久化**：支持角色数据的保存，自动保存到 `./characters_output/` 目录
- 🎮 **双重使用方式**：提供交互式界面（给普通用户）和代码接口（给程序员）
- 🔧 **高度可配置**：通过JSON文件自定义角色属性

## 🏗️ 项目结构

```
character-generator/
├── character_generator.py      # 主角色生成器类
├── character_generator_core.py # 核心生成逻辑
├── character_types.py         # 角色类型管理
├── file_loader.py             # 文件加载器
├── ui_manager.py              # 用户界面管理
├── main.py                    # 主程序入口
├── programmatic_demo.py       # 程序化使用演示
├── characters/                # 角色配置文件夹
│   └── woman/
│       ├── general.json       # 基础角色模板
│       ├── morecharacters.json # 角色变化数据
│       └── character_type.json # 角色类型定义
├── characters_output/         # 生成的角色保存目录
└── README.md                  # 项目说明文档
```

## 🚀 快速开始

### 安装依赖

```bash
# 本项目只依赖Python标准库，无需额外安装
python --version  # 需要Python 3.6+
```

### 交互式使用

```bash
# 启动交互式角色生成器
python main.py
```

### 在代码中使用

```python
from character_generator import CharacterGenerator

# 创建生成器实例
generator = CharacterGenerator()

# 生成创意型角色
creative_char = generator.generate_character("creative")

# 生成治愈师职业角色
healer_char = generator.generate_archetype("治愈师")

# 分析角色特征
traits = generator.analyze_traits(creative_char)
print(f"角色特征: {traits}")
```

## 📖 详细使用指南

### 交互式界面

启动程序后，你将看到以下选项：

1. **按性格特征生成**
   - 关爱型：温暖、关怀他人
   - 分析型：理性、逻辑思维
   - 创意型：创新、想象力丰富
   - 传统型：稳重、遵循传统
   - 进取型：积极、追求成功

2. **按职业角色生成**
   - 治愈师：关怀他人、提供帮助
   - 学者：专注研究、知识渊博
   - 艺术家：富有创造力、追求美感
   - 守护者：保护他人、责任感强
   - 领袖：具有领导力、善于决策

### 在代码中使用（给程序员）

如果你是程序员，想要在自己的程序中使用角色生成功能，可以直接调用这些函数：

#### 基础角色生成

```python
# 生成指定类型的角色
character = generator.generate_character("creative")

# 生成随机角色
random_char = generator.generate_character("random")

# 生成职业原型角色
archetype_char = generator.generate_archetype("学者")
```

#### 高级角色操作

```python
# 融合两个角色 (70% char1 + 30% char2)
blended = generator.blend_characters(char1, char2, ratio=0.7)

# 角色变异 (30% 变异率)
mutated = generator.mutate_character(character, mutation_rate=0.3)

# 自定义特质生成
custom_traits = {
    "personality": {"openness": 4, "conscientiousness": 1},
    "background": {"education": 3, "family": 2}
}
custom_char = generator.generate_character(custom_traits=custom_traits)
```

#### 角色分析和保存

```python
# 分析角色特征倾向
traits = generator.analyze_traits(character)

# 保存角色到文件（自动保存到 ./characters_output/ 目录）
generator.save_character(character, "my_character.json")

# 打印角色摘要
generator.print_summary(character)
```

## 🎯 应用场景

### 游戏开发

```python
# 为游戏生成NPC团队
npc_roles = [
    ("村长", "traditional"),
    ("铁匠", "analytical"), 
    ("艺术家", "creative"),
    ("医师", "caring"),
    ("商人", "ambitious")
]

npcs = []
for name, role_type in npc_roles:
    char_data = generator.generate_character(role_type)
    npcs.append((name, char_data))
```

### 创作辅助

```python
# 批量生成小说角色
characters = []
for i in range(10):
    char = generator.generate_character("random")
    traits = generator.analyze_traits(char)
    characters.append({
        'character': char,
        'dominant_trait': max(traits, key=traits.get),
        'traits': traits
    })
```

### 数据分析

```python
# 分析角色特征分布
trait_distribution = {}
for i in range(100):
    char = generator.generate_character("random")
    traits = generator.analyze_traits(char)
    for trait, count in traits.items():
        trait_distribution[trait] = trait_distribution.get(trait, 0) + count
```

## 📁 文件组织

### 输入文件（配置）
- `characters/` - 角色配置文件夹，包含角色模板和变化数据

### 输出文件（生成结果）
- `characters_output/` - 所有生成的角色文件都会自动保存在这里
- 目录不存在时会自动创建
- 文件命名由用户指定，建议使用描述性的文件名

### 使用示例
```python
# 所有这些文件都会保存在 ./characters_output/ 目录中
generator.save_character(hero_char, "main_hero.json")
generator.save_character(villain_char, "main_villain.json") 
generator.save_character(npc_char, "village_elder.json")
```

## 🔧 配置文件

### 基础模板 (general.json)

定义角色的基本结构和默认属性：

```json
{
    "personality": {
        "openness": "默认开放性描述",
        "conscientiousness": "默认责任心描述"
    },
    "background": {
        "education": "默认教育背景",
        "family": "默认家庭背景"
    }
}
```

### 角色变化 (morecharacters.json)

定义每个属性的不同变体：

```json
{
    "personality": {
        "openness": [
            "保守谨慎的性格",
            "适度开放的态度", 
            "非常开放和冒险"
        ]
    }
}
```

### 角色类型 (character_type.json)

定义角色类型及其描述：

```json
{
    "creative": {
        "value": 2,
        "description": "富有创造力和想象力的角色类型"
    },
    "analytical": {
        "value": 1,
        "description": "理性分析和逻辑思维的角色类型"
    }
}
```

## 🎨 扩展开发

### 添加新的角色类型

1. 在 `character_type.json` 中添加新类型
2. 在 `morecharacters.json` 中为新类型添加对应的变体
3. 重启程序即可使用新类型

### 自定义角色属性

1. 修改 `general.json` 添加新的属性维度
2. 在 `morecharacters.json` 中为新属性添加变体
3. 核心逻辑会自动适配新属性

### 集成到其他项目

```python
# 最小化集成示例
from character_generator import CharacterGenerator

class YourGameClass:
    def __init__(self):
        self.char_gen = CharacterGenerator()
    
    def create_npc(self, npc_type):
        return self.char_gen.generate_character(npc_type)
```

## 🛠️ 开发与测试

### 运行演示

```bash
# 运行程序化使用演示
python programmatic_demo.py

# 运行交互式界面
python main.py
```

### 模块测试

```python
# 测试各个模块
from file_loader import FileLoader
from character_types import CharacterTypes
from character_generator_core import CharacterGeneratorCore

# 测试文件加载
data = FileLoader.load_json("test.json")

# 测试类型管理
types = CharacterTypes(data)
```


🔄 **最后更新**：202507