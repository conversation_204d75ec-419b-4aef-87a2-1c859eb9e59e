#---------------#
# 数据库相关参数
#---------------#

import os

# 默认参数，按照MySQL数据库特征修改，修改成服务器上的IP、Port的和密码,以及可以自定义一个数据库
MYSQL_HOST = "localhost"
MYSQL_USER = "root"
MYSQL_PASSWORD = "8888"
MYSQL_DATABASE = "testlocal"
MYSQL_PORT = 40000
MYSQL_CHARSET = "utf8mb4"

# 默认参数，按照Milvus数据库特征修改
MILVUS_URI = "http://localhost:19530"
MILVUS_TOKEN = "root:Milvus"

# 硅基流动的向量嵌入模型API Key和Base URL
SILICONFLOW_API_KEY = "sk-qxehwrwohkxucyroiqhqtuuqmeewjxwigpbppbmyjqsyoxne"
SILICONFLOW_BASE_URL = "https://api.siliconflow.cn/v1/"

# MongoDB的相关配置
MONGODB_ADDRESS = "mongodb://localhost:27017/"
