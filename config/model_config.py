"""
模型配置文件
统一管理模型相关的配置参数
"""

import os
from typing import Dict, Any

# 默认配置
DEFAULT_MODEL_CONFIG = {
    # OpenAI配置
    'openai_api_key': 'sk-22fcd1205b084aaf9cb00ccd506290c5',
    'openai_base_url': 'https://api.deepseek.com',
    
    # 大模型配置
    'llm_model': 'deepseek-chat',
    'max_tokens': 2000,
    'temperature': 0.7,
    
    # Embedding模型配置
    'embedding_model_name': 'Qwen/Qwen3-Embedding-0.6B',
    'embedding_api_token': 'sk-qxehwrwohkxucyroiqhqtuuqmeewjxwigpbppbmyjqsyoxne',
    
    
    # 线程池配置
    'max_workers': 5,
    
    # 缓存配置
    'use_embedding_cache': True,
    'use_model_cache': True,
}

def get_model_config() -> Dict[str, Any]:
    """
    获取模型配置
    
    Returns:
        Dict[str, Any]: 模型配置字典
    """
    config = DEFAULT_MODEL_CONFIG.copy()
    
    # 从环境变量覆盖配置
    if os.getenv('OPENAI_API_KEY'):
        config['openai_api_key'] = os.getenv('OPENAI_API_KEY')
    
    if os.getenv('OPENAI_BASE_URL'):
        config['openai_base_url'] = os.getenv('OPENAI_BASE_URL')
    
    if os.getenv('LLM_MODEL'):
        config['llm_model'] = os.getenv('LLM_MODEL')
    
    if os.getenv('EMBEDDING_MODEL_NAME'):
        config['embedding_model_name'] = os.getenv('EMBEDDING_MODEL_NAME')
    
    if os.getenv('EMBEDDING_API_TOKEN'):
        config['embedding_api_token'] = os.getenv('EMBEDDING_API_TOKEN')
    
    max_workers_env = os.getenv('MAX_WORKERS')
    if max_workers_env:
        config['max_workers'] = int(max_workers_env)
    
    return config

def update_model_config(updates: Dict[str, Any]) -> None:
    """
    更新模型配置
    
    Args:
        updates: 要更新的配置项
    """
    global DEFAULT_MODEL_CONFIG
    DEFAULT_MODEL_CONFIG.update(updates)

def get_llm_config() -> Dict[str, Any]:
    """
    获取大模型配置
    
    Returns:
        Dict[str, Any]: 大模型配置
    """
    config = get_model_config()
    return {
        'api_key': config['openai_api_key'],
        'base_url': config['openai_base_url'],
        'model': config['llm_model'],
        'max_tokens': config['max_tokens'],
        'temperature': config['temperature'],
        'max_workers': config['max_workers']
    }

def get_embedding_config() -> Dict[str, Any]:
    """
    获取embedding模型配置
    
    Returns:
        Dict[str, Any]: embedding模型配置
    """
    config = get_model_config()
    return {
        'model_name': config['embedding_model_name'],
        'api_token': config['embedding_api_token'],
        'use_cache': config['use_embedding_cache']
    } 