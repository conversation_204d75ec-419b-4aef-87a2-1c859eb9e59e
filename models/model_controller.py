import time
import uuid
import openai
import numpy as np
import json
import os
import inspect
from functools import wraps
import requests
from openai import OpenAI
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import re
import logging
from datetime import datetime

# 导入统一的日志记录器
from .logger import get_logger, setup_log_dir

# 设置日志目录为models文件夹
setup_log_dir(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs'))

# 创建日志记录器
logger = get_logger('ModelController')

def clean_reasoning_model_output(text):
    """
    清理推理模型输出中的<think>标签
    适配推理模型（如o1系列）的输出格式
    """
    if not text:
        return text
    
    # 移除<think>...</think>标签及其内容
    cleaned_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    # 清理可能产生的多余空白行
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
    # 移除开头和结尾的空白
    cleaned_text = cleaned_text.strip()
    
    return cleaned_text

class ModelController:
    """模型控制器，统一管理大模型和embedding模型的调用"""
    
    def __init__(self, config):
        """
        初始化模型控制器
        
        Args:
            config: 配置对象，包含模型相关配置
        """
        self.config = config
        self._embedding_cache = {}
        self._model_cache = {}
        
        # 初始化OpenAI客户端
        self.openai_client = OpenAI(
            api_key=config.get('openai_api_key'),
            base_url=config.get('openai_base_url', "https://api.openai.com/v1")
        )
        
        # 初始化线程池，添加超时和错误处理
        max_workers = config.get('max_workers', 5)
        self.executor = ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="ModelController"
        )
        self._lock = threading.Lock()
        
        # 添加线程状态跟踪
        self._active_threads = set()
        self._shutdown_event = threading.Event()
        
        logger.info(f"ModelController初始化完成，线程池大小: {max_workers}")
    def _handle_non_stream_response(self, response, request_id, start_time):
        """处理非流式响应"""
        raw_content = response.choices[0].message.content
        if raw_content is None:
            logger.error(f"[{request_id}] 模型返回空响应")
            return "Error: Empty response from LLM."
        
        raw_content = raw_content.strip()
        # 自动清理推理模型的<think>标签
        cleaned_content = clean_reasoning_model_output(raw_content)
        
        elapsed_time = time.time() - start_time
        logger.info(f"[{request_id}] 调用成功 - 耗时: {elapsed_time:.2f}秒, 响应长度: {len(cleaned_content)}字符")
        
        return cleaned_content
    
    def _handle_stream_response(self, stream, request_id, start_time):
        """处理流式响应"""
        def stream_generator():
            try:
                full_content = ""
                chunk_count = 0
                
                for chunk in stream:
                    chunk_count += 1
                    
                    # 检查是否有内容
                    if (chunk.choices and 
                        len(chunk.choices) > 0 and 
                        chunk.choices[0].delta and 
                        chunk.choices[0].delta.content):
                        
                        content = chunk.choices[0].delta.content
                        full_content += content
                        yield content
                
                # 流式响应完成后的清理和日志
                elapsed_time = time.time() - start_time
                cleaned_content = clean_reasoning_model_output(full_content)
                
                logger.info(f"[{request_id}] 流式调用完成 - 耗时: {elapsed_time:.2f}秒, "
                           f"块数: {chunk_count}, 响应长度: {len(cleaned_content)}字符")
                
                # 如果需要，可以在最后yield清理后的差异部分
                if cleaned_content != full_content:
                    # 这里可以根据需要处理<think>标签的清理
                    # 但在流式输出中，通常不需要额外处理
                    pass
                    
            except Exception as e:
                elapsed_time = time.time() - start_time
                logger.error(f"[{request_id}] 流式调用出错 - 耗时: {elapsed_time:.2f}秒, 错误: {str(e)}")
                # 在流式输出中遇到错误时，可以选择yield错误信息或者静默结束
                yield f"Error: {str(e)}"
        
        return stream_generator()
    def chat_completion(self, model, messages, temperature=0.7, max_tokens=2000,stream=False):
        """
        调用大模型进行对话补全
        
        Args:
            model: 模型名称
            messages: 消息列表
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            str: 模型响应内容
        """
        start_time = time.time()
        request_id = str(uuid.uuid4())[:8]
        
        logger.info(f"[{request_id}] 开始调用OpenAI API - 模型: {model}, 温度: {temperature}, 最大tokens: {max_tokens}")
        logger.info(f"[{request_id}] 消息数量: {len(messages)}")
        
        try:
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream
            )
            if stream:
                # 流式输出：返回生成器
                return self._handle_stream_response(response, request_id, start_time)
            else:
                # 非流式输出：返回完整响应
                return self._handle_non_stream_response(response, request_id, start_time)
                
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"[{request_id}] 调用失败 - 耗时: {elapsed_time:.2f}秒, 错误: {str(e)}")
            if stream:
                # 流式错误处理：返回空生成器
                return iter([])
            else:
                return "Error: Could not get response from LLM."

    def chat_completion_async(self, model, messages, temperature=0.7, max_tokens=2000):
        """异步版本的chat_completion，添加超时处理"""
        if self._shutdown_event.is_set():
            logger.warning("ModelController已关闭，拒绝新的异步请求")
            return None
            
        future = self.executor.submit(self.chat_completion, model, messages, temperature, max_tokens)
        
        # 添加线程跟踪
        with self._lock:
            self._active_threads.add(future)
        
        return future

    def batch_chat_completion(self, requests):
        """
        并行处理多个LLM请求，添加超时和错误处理
        requests: List of dict with keys: model, messages, temperature, max_tokens
        """
        logger.info(f"开始批量处理 {len(requests)} 个请求")
        start_time = time.time()
        
        futures = []
        for i, req in enumerate(requests):
            if self._shutdown_event.is_set():
                logger.warning("ModelController已关闭，停止批量处理")
                break
                
            future = self.chat_completion_async(
                model=req.get("model", "gpt-4o-mini"),
                messages=req["messages"],
                temperature=req.get("temperature", 0.7),
                max_tokens=req.get("max_tokens", 2000)
            )
            if future:
                futures.append((i, future))
        
        results = []
        for i, future in futures:
            try:
                # 添加超时处理（5分钟）
                result = future.result(timeout=300)
                results.append(result)
                logger.info(f"批量请求 {i+1}/{len(requests)} 完成")
            except Exception as e:
                logger.error(f"批量请求 {i+1}/{len(requests)} 失败: {e}")
                results.append("Error: Could not get response from LLM.")
            finally:
                # 清理线程跟踪
                with self._lock:
                    self._active_threads.discard(future)
        
        elapsed_time = time.time() - start_time
        logger.info(f"批量处理完成 - 总耗时: {elapsed_time:.2f}秒")
        
        return results

    def get_embedding(self, text, model_name=None, use_cache=True, **kwargs):
        """
        获取文本的embedding向量
        
        Args:
            text: 输入文本
            model_name: 模型名称，如果为None则使用配置中的默认模型
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            numpy.ndarray: 文本的embedding向量
        """
        if model_name is None:
            model_name = self.config.get('embedding_model_name', "BAAI/bge-large-zh-v1.5")
        
        api_token = self.config.get('embedding_api_token')
        if not api_token:
            logger.error("缺少embedding API访问令牌")
            raise ValueError("必须提供embedding API的访问令牌")
        
        # 构建缓存键
        cache_key = f"{model_name}::{hash(text)}"
        
        if use_cache and cache_key in self._embedding_cache:
            logger.info(f"使用缓存的embedding - 模型: {model_name}, 文本长度: {len(text)}")
            return self._embedding_cache[cache_key]
        
        start_time = time.time()
        logger.info(f"开始获取embedding - 模型: {model_name}, 文本长度: {len(text)}")
        
        # 调用API获取embedding
        url = "https://api.siliconflow.cn/v1/embeddings"
        payload = {
            "model": model_name,
            "input": text
        }
        headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(url, json=payload, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            if 'data' in result and len(result['data']) > 0:
                embedding = np.array(result['data'][0]['embedding'])
                
                # 缓存结果
                if use_cache:
                    self._embedding_cache[cache_key] = embedding
                
                elapsed_time = time.time() - start_time
                logger.info(f"embedding获取成功 - 耗时: {elapsed_time:.2f}秒, 向量维度: {len(embedding)}")
                
                return embedding
            else:
                logger.error("API响应格式错误")
                raise ValueError("API响应格式错误")
                
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"获取embedding失败 - 耗时: {elapsed_time:.2f}秒, 错误: {str(e)}")
            raise
    
    def clear_embedding_cache(self):
        """清空embedding缓存"""
        cache_size = len(self._embedding_cache)
        self._embedding_cache.clear()
        logger.info(f"已清空embedding缓存 - 清理了 {cache_size} 个缓存项")
    
    def get_thread_status(self):
        """获取线程池状态信息"""
        with self._lock:
            active_count = len(self._active_threads)
        
        return {
            "active_threads": active_count,
            "max_workers": self.executor._max_workers,
            "shutdown": self._shutdown_event.is_set()
        }

    def shutdown(self):
        """关闭线程池，添加更好的清理机制"""
        logger.info("正在关闭ModelController...")
        
        # 设置关闭标志
        self._shutdown_event.set()
        
        # 等待所有活跃线程完成
        with self._lock:
            active_count = len(self._active_threads)
            if active_count > 0:
                logger.info(f"等待 {active_count} 个活跃线程完成...")
        
        # 关闭线程池，等待最多30秒
        try:
            self.executor.shutdown(wait=True)
            logger.info("ModelController线程池已关闭")
        except Exception as e:
            logger.error(f"关闭线程池时出错: {e}")
        
        # 清理缓存
        self.clear_embedding_cache()
        logger.info("ModelController已完全关闭")

# 兼容性函数，保持向后兼容
def get_embedding(text, model_name="BAAI/bge-large-zh-v1.5", use_cache=True, api_token=None, **kwargs):
    """
    兼容性函数，用于向后兼容
    """
    from config.model_config import get_model_config
    config = get_model_config()
    if api_token:
        config['embedding_api_token'] = api_token
    
    controller = ModelController(config)
    return controller.get_embedding(text, model_name, use_cache, **kwargs)

class OpenAIClient:
    """
    兼容性类，保持向后兼容
    """
    def __init__(self, api_key, base_url=None, max_workers=5):
        config = {
            'openai_api_key': api_key,
            'openai_base_url': base_url,
            'max_workers': max_workers
        }
        self.controller = ModelController(config)
    
    def chat_completion(self, model, messages, temperature=0.7, max_tokens=2000):
        return self.controller.chat_completion(model, messages, temperature, max_tokens)
    
    def chat_completion_async(self, model, messages, temperature=0.7, max_tokens=2000):
        return self.controller.chat_completion_async(model, messages, temperature, max_tokens)
    
    def batch_chat_completion(self, requests):
        return self.controller.batch_chat_completion(requests)
    
    def shutdown(self):
        self.controller.shutdown() 