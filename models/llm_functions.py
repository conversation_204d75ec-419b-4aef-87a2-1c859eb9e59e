"""
LLM函数模块
包含所有与模型调用相关的函数
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.model_controller import ModelController
from config.model_config import get_model_config
from prompt.prompts import (
    SUMMARIZE_DIALOGS_SYSTEM_PROMPT, SUMMARIZE_DIALOGS_USER_PROMPT,
    MULTI_SUMMARY_SYSTEM_PROMPT, MULTI_SUMMARY_USER_PROMPT,
    PERSONALITY_ANALYSIS_SYSTEM_PROMPT, PERSONALITY_ANALYSIS_USER_PROMPT,
    KNOWLEDGE_EXTRACTION_SYSTEM_PROMPT, KNOWLEDGE_EXTRACTION_USER_PROMPT,
    UPDATE_PROFILE_SYSTEM_PROMPT, UPDATE_PROFILE_USER_PROMPT,
    EXTRACT_THEME_SYSTEM_PROMPT, EXTRACT_THEME_USER_PROMPT,
    CONTINUITY_CHECK_SYSTEM_PROMPT, CONTINUITY_CHECK_USER_PROMPT,
    META_INFO_SYSTEM_PROMPT, META_INFO_USER_PROMPT
)

# 导入日志记录器
from .logger import get_logger, setup_log_dir

# 设置日志目录为models文件夹
setup_log_dir(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs'))

logger = get_logger('LLMFunctions')

def get_model_controller():
    """获取模型控制器实例"""
    config = get_model_config()
    return ModelController(config)

def gpt_summarize_dialogs(dialogs, model_controller=None, model="gpt-4o-mini"):
    """总结对话"""
    if model_controller is None:
        model_controller = get_model_controller()
    
    dialog_text = "\n".join([f"User: {d.get('user_input','')} Assistant: {d.get('agent_response','')}" for d in dialogs])
    messages = [
        {"role": "system", "content": SUMMARIZE_DIALOGS_SYSTEM_PROMPT},
        {"role": "user", "content": SUMMARIZE_DIALOGS_USER_PROMPT.format(dialog_text=dialog_text)}
    ]
    logger.info("Calling LLM to generate topic summary...")
    return model_controller.chat_completion(model=model, messages=messages)

def gpt_generate_multi_summary(text, model_controller=None, model="gpt-4o-mini"):
    """生成多主题总结"""
    if model_controller is None:
        model_controller = get_model_controller()
    
    messages = [
        {"role": "system", "content": MULTI_SUMMARY_SYSTEM_PROMPT},
        {"role": "user", "content": MULTI_SUMMARY_USER_PROMPT.format(text=text)}
    ]
    logger.info("Calling LLM to generate multi-topic summary...")
    response_text = model_controller.chat_completion(model=model, messages=messages)
    try:
        summaries = json.loads(response_text)
    except json.JSONDecodeError:
        logger.warning(f"Could not parse multi-summary JSON: {response_text}")
        summaries = [] # Return empty list or a default structure
    return {"input": text, "summaries": summaries}

def gpt_user_profile_analysis(dialogs, model_controller=None, model="gpt-4o-mini", existing_user_profile="None"):
    """
    分析并更新用户个性档案
    结合现有画像和新对话，直接输出更新后的完整画像
    """
    if model_controller is None:
        model_controller = get_model_controller()
    
    conversation = "\n".join([f"User: {d.get('user_input','')} (Timestamp: {d.get('timestamp', '')})\nAssistant: {d.get('agent_response','')} (Timestamp: {d.get('timestamp', '')})" for d in dialogs])
    messages = [
        {"role": "system", "content": PERSONALITY_ANALYSIS_SYSTEM_PROMPT},
        {"role": "user", "content": PERSONALITY_ANALYSIS_USER_PROMPT.format(
            conversation=conversation,
            existing_user_profile=existing_user_profile
        )}
    ]
    logger.info("Calling LLM for user profile analysis and update...")
    result_text = model_controller.chat_completion(model=model, messages=messages)
    return result_text.strip() if result_text else "None"

def gpt_knowledge_extraction(dialogs, model_controller=None, model="gpt-4o-mini"):
    """从对话中提取用户私人数据和助手知识"""
    if model_controller is None:
        model_controller = get_model_controller()
    
    conversation = "\n".join([f"User: {d.get('user_input','')} (Timestamp: {d.get('timestamp', '')})\nAssistant: {d.get('agent_response','')} (Timestamp: {d.get('timestamp', '')})" for d in dialogs])
    messages = [
        {"role": "system", "content": KNOWLEDGE_EXTRACTION_SYSTEM_PROMPT},
        {"role": "user", "content": KNOWLEDGE_EXTRACTION_USER_PROMPT.format(
            conversation=conversation
        )}
    ]
    logger.info("Calling LLM for knowledge extraction...")
    result_text = model_controller.chat_completion(model=model, messages=messages)
    
    private_data = "None"
    assistant_knowledge = "None"

    try:
        if "【用户私人数据】" in result_text:
            private_data_start = result_text.find("【用户私人数据】") + len("【用户私人数据】")
            if "【助手知识】" in result_text:
                private_data_end = result_text.find("【助手知识】")
                private_data = result_text[private_data_start:private_data_end].strip()
                
                assistant_knowledge_start = result_text.find("【助手知识】") + len("【助手知识】")
                assistant_knowledge = result_text[assistant_knowledge_start:].strip()
            else:
                private_data = result_text[private_data_start:].strip()
        elif "【助手知识】" in result_text:
             assistant_knowledge_start = result_text.find("【助手知识】") + len("【助手知识】")
             assistant_knowledge = result_text[assistant_knowledge_start:].strip()

    except Exception as e:
        logger.error(f"Error parsing knowledge extraction: {e}. Raw result: {result_text}")

    return {
        "private": private_data if private_data else "None", 
        "assistant_knowledge": assistant_knowledge if assistant_knowledge else "None"
    }

def gpt_personality_analysis(dialogs, model_controller=None, model="gpt-4o-mini", known_user_traits="None"):
    """
    DEPRECATED: Use gpt_user_profile_analysis and gpt_knowledge_extraction instead.
    This function is kept for backward compatibility only.
    """
    # Call the new functions
    profile = gpt_user_profile_analysis(dialogs, model_controller, model, known_user_traits)
    knowledge_data = gpt_knowledge_extraction(dialogs, model_controller, model)
    
    return {
        "profile": profile,
        "private": knowledge_data["private"],
        "assistant_knowledge": knowledge_data["assistant_knowledge"]
    }

def gpt_update_profile(old_profile, new_analysis, model_controller=None, model="gpt-4o-mini"):
    """更新用户档案"""
    if model_controller is None:
        model_controller = get_model_controller()
    
    messages = [
        {"role": "system", "content": UPDATE_PROFILE_SYSTEM_PROMPT},
        {"role": "user", "content": UPDATE_PROFILE_USER_PROMPT.format(old_profile=old_profile, new_analysis=new_analysis)}
    ]
    logger.info("Calling LLM to update user profile...")
    return model_controller.chat_completion(model=model, messages=messages)

def gpt_extract_theme(answer_text, model_controller=None, model="gpt-4o-mini"):
    """提取主题"""
    if model_controller is None:
        model_controller = get_model_controller()
    
    messages = [
        {"role": "system", "content": EXTRACT_THEME_SYSTEM_PROMPT},
        {"role": "user", "content": EXTRACT_THEME_USER_PROMPT.format(answer_text=answer_text)}
    ]
    logger.info("Calling LLM to extract theme...")
    return model_controller.chat_completion(model=model, messages=messages)

def check_conversation_continuity(previous_page, current_page, model_controller=None, model="gpt-4o-mini"):
    """检查对话连续性"""
    if model_controller is None:
        model_controller = get_model_controller()
    
    prev_user = previous_page.get("user_input", "") if previous_page else ""
    prev_agent = previous_page.get("agent_response", "") if previous_page else ""
    
    user_prompt = CONTINUITY_CHECK_USER_PROMPT.format(
        prev_user=prev_user,
        prev_agent=prev_agent,
        curr_user=current_page.get("user_input", ""),
        curr_agent=current_page.get("agent_response", "")
    )
    messages = [
        {"role": "system", "content": CONTINUITY_CHECK_SYSTEM_PROMPT},
        {"role": "user", "content": user_prompt}
    ]
    response = model_controller.chat_completion(model=model, messages=messages, temperature=0.0, max_tokens=10)
    return response.strip().lower() == "true"

def generate_page_meta_info(last_page_meta, current_page, model_controller=None, model="gpt-4o-mini"):
    """生成页面元信息"""
    if model_controller is None:
        model_controller = get_model_controller()
    
    current_conversation = f"User: {current_page.get('user_input', '')}\nAssistant: {current_page.get('agent_response', '')}"
    user_prompt = META_INFO_USER_PROMPT.format(
        last_meta=last_page_meta if last_page_meta else "None",
        new_dialogue=current_conversation
    )
    messages = [
        {"role": "system", "content": META_INFO_SYSTEM_PROMPT},
        {"role": "user", "content": user_prompt}
    ]
    return model_controller.chat_completion(model=model, messages=messages, temperature=0.3, max_tokens=100).strip() 