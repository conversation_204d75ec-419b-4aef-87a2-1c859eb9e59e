"""
统一的日志记录器模块
提供统一的日志记录功能，支持文件输出，不在控制台显示
"""

import os
import logging
from datetime import datetime
from typing import Optional


class LoggerManager:
    """日志管理器，负责创建和管理日志记录器"""
    
    _loggers = {}
    _log_dir = None
    
    @classmethod
    def setup_log_dir(cls, log_dir: Optional[str] = None):
        """设置日志目录"""
        if log_dir is None:
            # 默认在项目根目录下创建logs目录
            cls._log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
        else:
            cls._log_dir = log_dir
        
        # 创建日志目录（如果不存在）
        os.makedirs(cls._log_dir, exist_ok=True)
    
    @classmethod
    def get_logger(cls, name: str, log_level: int = logging.INFO) -> logging.Logger:
        """
        获取或创建日志记录器
        
        Args:
            name: 日志记录器名称
            log_level: 日志级别
            
        Returns:
            logging.Logger: 日志记录器实例
        """
        # 如果日志目录未设置，先设置默认目录
        if cls._log_dir is None:
            cls.setup_log_dir()
        
        # 如果已经存在该名称的logger，直接返回
        if name in cls._loggers:
            return cls._loggers[name]
        
        # 创建新的logger
        logger = logging.getLogger(name)
        logger.setLevel(log_level)
        
        # 避免重复添加handler
        if not logger.handlers:
            # 创建日志文件名（按日期）
            if cls._log_dir is not None:
                log_filename = os.path.join(cls._log_dir, f'{name}_{datetime.now().strftime("%Y%m%d")}.log')
            else:
                # 如果_log_dir为None，使用默认路径
                default_log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
                log_filename = os.path.join(default_log_dir, f'{name}_{datetime.now().strftime("%Y%m%d")}.log')
            
            # 创建文件处理器
            file_handler = logging.FileHandler(log_filename, encoding='utf-8')
            file_handler.setLevel(log_level)
            
            # 创建格式器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            file_handler.setFormatter(formatter)
            
            # 添加处理器到logger
            logger.addHandler(file_handler)
        
        # 缓存logger
        cls._loggers[name] = logger
        
        return logger
    
    @classmethod
    def clear_loggers(cls):
        """清空所有日志记录器缓存"""
        cls._loggers.clear()


def get_logger(name: str, log_level: int = logging.INFO) -> logging.Logger:
    """
    获取日志记录器的便捷函数
    
    Args:
        name: 日志记录器名称
        log_level: 日志级别
        
    Returns:
        logging.Logger: 日志记录器实例
    """
    return LoggerManager.get_logger(name, log_level)


def setup_log_dir(log_dir: str):
    """
    设置日志目录的便捷函数
    
    Args:
        log_dir: 日志目录路径
    """
    LoggerManager.setup_log_dir(log_dir)


# 默认日志记录器，用于向后兼容
default_logger = get_logger('default') 