import requests
if __name__ == "__main__":

    while(True):
        user_input = input("\n用户:")
        with requests.post(
            url ="http://47.122.92.170:8090/ai/stream_lingyu_chat/",
            json={
                "user_id":"13151589300",
                #"assistant_id":"50:78:7d:14:17:04",
                "assistant_id":"wuke_diannao",
                "user_input":user_input,
                "hotspot":True
            },
            stream=True,  # 启用流式接收
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lyapi_202507_K8mN3xP9qR7wE2vB5nM8jL4yU6tA1sD3fG9hJ0kZ2cV7bN4mX"
            }
        
        )as response:
            print("流式:")
            for chunk in response.iter_content(chunk_size=None):
                if chunk:  # 过滤掉保持连接的空行
                    print(chunk.decode('utf-8'), end='', flush=True)