import json
from fastapi import params
import requests
import httpx
def bocha_AIsearch_tool(query: str, count: int = 3) -> str:
    """
    使用Bocha Web Search API 进行网页搜索。

    参数:
    - query: 搜索关键词

    返回:
    - 搜索结果的详细信息，包括网页标题、网页URL、网页摘要、网站名称、网站Icon、网页发布时间等。
    """
    
    url = 'https://api.bochaai.com/v1/ai-search'
    headers = {
        'Authorization': f'Bearer sk-0ea2d2b4c3ea4f099fd33cab31e4c568',  # 请替换为你的API密钥
        'Content-Type': 'application/json'
    }
    data = {
        "query": query,
        "freshness": "oneWeek", # 搜索的时间范围，例如 "oneDay", "oneWeek", "oneMonth", "oneYear", "noLimit"
        "summary": True, # 是否返回长文本摘要
        "answer": False,
        "count": count
    }

    response = requests.post(url, headers=headers, json=data)
    if response.ok:
        response_json = response.json()
        result = {}
        if "messages" in response_json:
            for message in response_json["messages"]:
                content = {}
                try:
                    content = json.loads(message["content"])
                except Exception as e:
                    return ""
                # 网页
                if message["content_type"] == "webpage":
                    if "value" in content:
                        result["webpage"] = [
                        {
                            "id": item.get("id", ""),
                            "name": item.get("name", ""),
                            "url": item.get("url", ""),
                            "snippet": item.get("snippet", ""),
                            "summary": item.get("summary", ""),
                            "siteName": item.get("siteName", ""),
                            "siteIcon": item.get("siteIcon", ""),
                            "datePublished": item.get("datePublished", "") or item.get("dateLastCrawled", ""),
                        }
                        for item in content["value"]
                    ]
            formatted_webpage  = ""
            for idx, page in enumerate(result["webpage"], start=1):
                formatted_webpage += (
                    f"标题: {page['name']}\n"
                    f"摘要: {page['summary']}\n"
                    f"发布时间: {page['datePublished']}\n\n"
                )
            formatted_webpage = formatted_webpage.strip()
            return formatted_webpage
        return ""
    return ""

def search_engine(query,tool_id,messages):
    formatted_webpage = bocha_AIsearch_tool(query=query)
    jishu_stock = ""
    messages.append({
        'role': 'tool',
        'content': formatted_webpage,
        'tool_call_id': tool_id
    })
    return messages


import datetime

def ask_time(delta_days=0, delta_hours=0, delta_minutes=0, delta_seconds=0, tool_id=None, messages=None):
    """
    根据与当前时间的差值（天、小时、分钟、秒），返回对应的时间字符串（含星期几），并加入messages。
    """
    import datetime
    if messages is None:
        messages = []
    target_time = datetime.datetime.now() + datetime.timedelta(
        days=delta_days,
        hours=delta_hours,
        minutes=delta_minutes,
        seconds=delta_seconds
    )
    week_map = {
        0: "星期一",
        1: "星期二",
        2: "星期三",
        3: "星期四",
        4: "星期五",
        5: "星期六",
        6: "星期日"
    }
    week_day = week_map[target_time.weekday()]
    time_str = target_time.strftime("%Y-%m-%d %H:%M:%S") + f" {week_day}"
    messages.append({
        'role': 'tool',
        'content': time_str,
        'tool_call_id': tool_id
    })
    return messages

def get_weather(location_name,tool_id=None, messages=None):
    location ,name= get_location_id(location_name)
    url = f"https://kk2k5mx23b.re.qweatherapi.com/v7/weather/now?location={location}&key=76b4356792724d30abfa2c1fa990b5e2"
    try:
        response = requests.get(url)
        response.raise_for_status()  # 检查请求是否成功
        data = response.json()
        
        if data['code'] != '200':
            return f"请求失败，错误代码: {data['code']}"
            
        now = data['now']
        messages.append({
        'role': 'tool',
        'content': (
            f"位置: {name}, "
            f"天气情况: {now['text']}, "
            f"气温: {now['temp']}°C, "
            f"风速: {now['windSpeed']}km/h, "
            f"湿度: {now['humidity']}%"
        ),
        'tool_call_id': tool_id
        })
        return messages
    except requests.exceptions.RequestException as e:
        return f"请求出错: {str(e)}"
    except (KeyError, json.JSONDecodeError) as e:
        return f"解析响应数据出错: {str(e)}"

def get_location_id(location_name):
    """
    根据地点名称获取最匹配的位置ID
    
    :param location_name: 地点名称(如"beij")
    :param api_token: API访问令牌
    :return: 最匹配的位置ID，如果没有结果则返回None
    """
    url = f"https://kk2k5mx23b.re.qweatherapi.com/geo/v2/city/lookup?location={location_name}&key=76b4356792724d30abfa2c1fa990b5e2"
    # headers = {
    #     'Authorization': f'Bearer {api_token}',
    #     'Accept-Encoding': 'gzip'
    # }
    
    try:
        response = requests.get(url)
        response.raise_for_status()  # 检查请求是否成功
        data = response.json()
        
        if data['code'] != '200' or not data.get('location'):
            return None
            
        # 返回第一个结果的ID
        return data['location'][0]['id'],data['location'][0]['name']
        
    except requests.exceptions.RequestException as e:
        print(f"请求出错: {str(e)}")
        return None
    except (KeyError, IndexError) as e:
        print(f"解析响应数据出错: {str(e)}")
        return None
# 使用示例

        
if __name__ == "__main__":
    
    ask_time(delta_days=1)
    # location = input("请输入查询地点（如：beijing）：")
    # weather_message = get_weather_message(location)
    # api_token = "your_token"
    # location = "101010100"  # 北京的区域代码
    # weather_info = get_weather(location, api_token)
    # print(weather_info)
    print(search_engine("微博热搜",12345,[]))