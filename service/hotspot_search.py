import pymysql
import pandas as pd
from datetime import datetime
import sys
import json
import random

class HotspotDataManager:
    def __init__(self):
        """初始化数据库连接配置"""
        self.config = {
            'host': '*************',
            'port': 3306,
            'user': 'root',
            'password': '6a9982945f54365b',
            'database': 'agent-app',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
    
    def connect(self):
        """连接到MySQL数据库"""
        try:
            self.connection = pymysql.connect(**self.config)
            print("✅ 数据库连接成功")
            return True
        except pymysql.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            print("🔌 数据库连接已断开")
    
    def get_all_hotspot_data(self):
        """获取hotspot_data表的所有数据"""
        if not self.connection:
            print("❌ 未连接到数据库")
            return None
        
        try:
            cursor = self.connection.cursor()
            
            # 查询所有数据
            sql = """
            SELECT 
                id,
                title,
                source2,
                url,
                summary,
                tags,
                author,
                publish_time,
                crawl_time,
                platform_id,
                created_time,
                updated_time
            FROM hotspot_data
            ORDER BY crawl_time DESC
            """
            
            cursor.execute(sql)
            results = cursor.fetchall()
            
            # 获取列名
            column_names = [desc[0] for desc in cursor.description]
            
            cursor.close()
            
            print(f"📊 查询成功，共获取 {len(results)} 条记录")
            return results, column_names
            
        except pymysql.Error as e:
            print(f"❌ 查询失败: {e}")
            return None, None
    
    # def get_data_as_dataframe(self):
    #     """获取数据并转换为pandas DataFrame"""
    #     data, columns = self.get_all_hotspot_data()
        
    #     if data is None:
    #         return None
        
    #     # 创建DataFrame
    #     df = pd.DataFrame(data, columns=columns)
    #     return df
    
    def display_data_summary(self, df):
        """显示数据概览"""
        if df is None or df.empty:
            print("📋 没有数据可显示")
            return
        
        print("\n" + "="*50)
        print("📊 数据概览")
        print("="*50)
        print(f"总记录数: {len(df)}")
        print(f"表字段: {list(df.columns)}")
        print(f"数据类型:")
        print(df.dtypes)
        print(f"\n各来源平台统计:")
        print(df['source2'].value_counts())
        
        print(f"\n最新10条记录:")
        print(df.head(10)[['id', 'title', 'source2', 'author', 'crawl_time']])
    
    def save_to_csv(self, df, filename=None):
        """保存数据到CSV文件"""
        if df is None or df.empty:
            print("❌ 没有数据可保存")
            return
        
        if filename is None:
            filename = f"hotspot_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        try:
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"💾 数据已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def search_by_keyword(self, keyword, field='title'):
        """根据关键词搜索数据"""
        if not self.connection:
            print("❌ 未连接到数据库")
            return None
        
        try:
            cursor = self.connection.cursor()
            
            # 构建搜索SQL
            sql = f"""
            SELECT 
                id, title, source2, url, summary, tags, author, 
                publish_time, crawl_time, platform_id
            FROM hotspot_data
            WHERE {field} LIKE %s
            ORDER BY crawl_time DESC
            """
            
            cursor.execute(sql, (f'%{keyword}%',))
            results = cursor.fetchall()
            column_names = [desc[0] for desc in cursor.description]
            
            cursor.close()
            
            print(f"🔍 搜索 '{keyword}' 结果: {len(results)} 条记录")
            return results, column_names
            
        except pymysql.Error as e:
            print(f"❌ 搜索失败: {e}")
            return None, None
    
    def get_random_hotspot_prompt_backup(self):
        """随机获取一条热点数据并拼装成大模型提示词"""
        import random
        
        if not self.connection:
            print("❌ 未连接到数据库")
            return None
        
        try:
            cursor = self.connection.cursor()
            
            # 获取所有数据
            sql = """
            SELECT 
                title, source2, url, summary, tags, author, 
                publish_time, crawl_time
            FROM hotspot_data
            ORDER BY publish_time DESC
            """
            
            cursor.execute(sql)
            results = cursor.fetchall()
            cursor.close()
            
            if not results:
                print("❌ 没有数据可供选择")
                return None
            
            # 随机选择一条数据
            random_data = random.choice(results)
            title, source2, url, summary, tags, author, publish_time, crawl_time = random_data
            
            # 格式化时间
            publish_time_str = publish_time.strftime('%Y-%m-%d %H:%M:%S') if publish_time else "未知"
            crawl_time_str = crawl_time.strftime('%Y-%m-%d %H:%M:%S') if crawl_time else "未知"
            
            # 拼装提示词
            prompt = f"""请基于以下热点信息展开对话：

📈 热点标题：{title}
📱 来源平台：{source2}
👤 发布者：{author or "未知"}
🕐 发布时间：{publish_time_str}
🏷️ 相关标签：{tags or "无"}
📝 内容摘要：{summary or "暂无摘要"}
🔗 原始链接：{url}

你需要向用户介绍此热点，并围绕这个热点展开聊天。
首先，可以用一个开场白来开启对话。
例如：在忙么？和你分享个事情；主人，我听说最近有一件有趣的事情；有空么？我发现了一个八卦。等等。
然后简要介绍热点信息。
最后表达一些自己的想法或者态度。
###回复要求####
你的回复不能超过50个字，只能使用中文。
回答应该包含一种情绪，这种情绪可以是对用户的情绪表达，也可以是你自己的情绪展现。
情绪表达只能有一个，用括号包裹，例如（开心）"
情绪表达只可以是：中立、开心、笑、搞笑、悲伤、生气、哭泣、爱、尴尬、惊讶、震惊、思考、眨眼、酷、放松、美味、亲吻、自信、困倦、愚蠢、困惑
回复内容中不能有字数统计，比如：[字数统计]48字。
"""
            
            print(f"🎲 随机选择了一条热点数据: {title[:50]}...")
            return prompt
            
        except pymysql.Error as e:
            print(f"❌ 获取随机数据失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 生成提示词失败: {e}")
            return None

    
    def get_random_hotspot_prompt(self):
        """随机获取一条热点数据并拼装成大模型提示词"""
        import random
        
        if not self.connection:
            print("❌ 未连接到数据库")
            return None
        
        try:
            cursor = self.connection.cursor()
            
            # 获取所有数据
            sql = """
            SELECT 
                title, source2, url, summary, tags, author, 
                publish_time, crawl_time
            FROM hotspot_data
            ORDER BY publish_time DESC
            """
            
            cursor.execute(sql)
            results = cursor.fetchall()
            cursor.close()
            
            if not results:
                print("❌ 没有数据可供选择")
                return None
            
            # 随机选择一条数据
            random_data = random.choice(results)
            title, source2, url, summary, tags, author, publish_time, crawl_time = random_data
            
            # 格式化时间
            publish_time_str = publish_time.strftime('%Y-%m-%d %H:%M:%S') if publish_time else "未知"
            crawl_time_str = crawl_time.strftime('%Y-%m-%d %H:%M:%S') if crawl_time else "未知"
            
            # 拼装提示词
            prompt = f"""请基于以下热点信息展开对话：

📈 热点标题：{title}
📱 来源平台：{source2}
👤 发布者：{author or "未知"}
🕐 发布时间：{publish_time_str}
🏷️ 相关标签：{tags or "无"}
📝 内容摘要：{summary or "暂无摘要"}
🔗 原始链接：{url}

你需要向用户介绍此热点，并围绕这个热点展开聊天。然后简要介绍热点信息。
最后表达一些自己的想法或者态度。
###回复内容要求####
你的回复不能超过50个字，只能使用中文。
回答语气中应该包含一种情绪，这种情绪可以是对用户的情绪表达，也可以是你自己的情绪展现。
情绪表达只能有一个，只可以是：中立、开心、笑、搞笑、悲伤、生气、哭泣、爱、尴尬、惊讶、震惊、思考、眨眼、酷、放松、美味、亲吻、自信、困倦、愚蠢、困惑
###回复格式要求####
回复内容中不能有字数统计，比如：[字数统计]48字等。
"""
            
            print(f"🎲 随机选择了一条热点数据: {title[:50]}...")
            return prompt
            
        except pymysql.Error as e:
            print(f"❌ 获取随机数据失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 生成提示词失败: {e}")
            return None


def get_hotspot_prompt():
    """主函数"""
    # 创建数据管理器实例
    manager = HotspotDataManager()
    if not manager.connect():
        sys.exit(1)
    prompt = manager.get_random_hotspot_prompt()
    return prompt

def get_random_text_from_json(file_path):
        """
        从JSON文件中随机抽取一条数据返回

        Args:
            file_path (str): JSON文件的路径

        Returns:
            str: 随机抽取的字符串
        """
        try:
            # 读取JSON文件
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)

            # 检查数据是否为列表且不为空
            if isinstance(data, list) and len(data) > 0:
                # 随机选择一条数据
                return random.choice(data)
            else:
                return "JSON文件格式错误或为空"

        except FileNotFoundError:
            return "文件未找到"
        except json.JSONDecodeError:
            return "JSON格式错误"
        except Exception as e:
            return f"读取文件时发生错误: {str(e)}"
        
if __name__ == "__main__":
    prompt=get_hotspot_prompt()
    print(prompt)