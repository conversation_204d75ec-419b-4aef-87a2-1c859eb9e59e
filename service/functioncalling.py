tools = [ 
    # 工具2 通过搜索引擎搜索信息
    {
        "type": "function",
        "function": {
            "name": "search_engine",
            "description": "当用户提出时效性较强、需要最新信息或模型自身知识无法覆盖的问题时（如近期新闻、最新电影、经济形势等），可调用此工具进行联网检索，获取互联网的实时公开信息。",
            "parameters": {  
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "用于搜索的具体请求内容，应该根据用户的具体问题改写为适合互联网检索的语句。"
                    }
                },
                "required": ["query"]  # 必填参数
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "获取指定地区的实时天气信息，包括天气状况、温度、风速和湿度等。",
            "parameters": {
                "type": "object",
                "properties": {
                    "location_name": {
                        "type": "string",
                        "description": "需要查询天气的地区名称，可以是城市、区县或具体地点。例如北京、玄武区、朝阳区等"
                    }
                },
                "required": ["location_name"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "ask_time",
            "description": "当用户询问时间相关问题时，可以使用。可以查询当前时间或者与当前时间有明确间隔的时间日期等。",
            "parameters": {
                "type": "object",
                "properties": {
                    "delta_days": {
                        "type": "integer",
                        "description": "与当前时间相差的天数，可正可负，默认为0"
                    },
                    "delta_hours": {
                        "type": "integer",
                        "description": "与当前时间相差的小时数，可正可负，默认为0"
                    },
                    "delta_minutes": {
                        "type": "integer",
                        "description": "与当前时间相差的分钟数，可正可负，默认为0"
                    },
                    "delta_seconds": {
                        "type": "integer",
                        "description": "与当前时间相差的秒数，可正可负，默认为0"
                    }
                },
                "required": []
            }
        }
    }
]