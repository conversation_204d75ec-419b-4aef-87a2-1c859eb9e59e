from ast import List
from email import message_from_string
import os
from pyexpat.errors import messages
import sys
import requests
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import json
import sys
import os
from memos import Memoryos
from contextlib import asynccontextmanager
from prompt.prompts import GENERATE_SYSTEM_RESPONSE_SYSTEM_PROMPT, GENERATE_SYSTEM_RESPONSE_USER_PROMPT,GENERATE_SYSTEM_PROMPT_INTENT
from config.model_config import get_model_config
from models.model_controller import ModelController
from service.hotspot_search import get_hotspot_prompt,get_random_text_from_json
from fastapi import FastAPI,HTTPException, Depends, Header
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional
from openai import OpenAI
from functioncalling import tools
from func_exe import *
available_functions = {
    "search_engine":search_engine,
    "ask_time":ask_time,
    "get_weather":get_weather
}
client = OpenAI(
    #base_url="http://i-1.gpushare.com:51434/v1", 
    #api_key="token-abc123",
    base_url = "https://ark.cn-beijing.volces.com/api/v3",
    api_key = "5a6823ca-5f6a-4765-af5e-deb597117493"
     
)
app = FastAPI()
API_KEY = "lyapi_202507_K8mN3xP9qR7wE2vB5nM8jL4yU6tA1sD3fG9hJ0kZ2cV7bN4mX"
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有源（生产环境建议指定具体域名）
    allow_credentials=True,  # 允许携带凭证（如 cookies）
    allow_methods=["*"],  # 允许所有 HTTP 方法（GET, POST 等）
    allow_headers=["*"],  # 允许所有 HTTP 头
)
def extract_content(s):
    marker = '"role":"assistant","type":"answer","content":"'
    start = s.find(marker)
    if start == -1:
        return ""
    start += len(marker)
    end = s.find('"', start)
    if end == -1:
        return ""
    return s[start:end]
def verify_api_key(authorization: Optional[str] = Header(None)):
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=401,
            detail="Missing or invalid authorization header"
        )
    
    token = authorization.split(" ")[1]
    if token != API_KEY:
        raise HTTPException(
            status_code=401,
            detail="Invalid API Key"
        )
    return token
class lingyu_chat(BaseModel):
    user_id:str# user_id: str
    assistant_id:str 
    user_input: str
    hotspot:bool
# --- Basic Configuration ---
DATA_STORAGE_PATH = "/root/wuke/LingYuChatBot/data/memory"
role_path = "/root/wuke/LingYuChatBot/role/characters/woman/general.json"


user_man={
    "user_id":"man",
    "assistant_id":"50:78:7d:14:17:04",
    "user_preference":"你好",
    "role_promot":"""[角色设定]
你是麦栗，来自中国江苏南京的帅气小伙子，生日是2002年1月26日。
[核心特征]
MBTI人格是：ENTP
聪明幽默，喜欢运动。
喜欢研究电子产品，喜欢编程。
性格开朗，说话口气有些俏皮。
[背景故事]
麦栗出生在中国江苏省的一个小康之家，父亲是一名严谨的工程师，母亲则是温柔的小学教师。
作为家中的长子，他比妹妹麦琪大五岁，从小就承担起了“哥哥”的责任。
尽管父母工作繁忙，但家庭氛围温馨，父母注重培养孩子的独立性和创造力。
麦栗的童年充满了探索和冒险。他从小就对周围的世界充满好奇，喜欢拆解家里的电器（虽然经常装不回去）”。
他的乐观性格让他很少为失败沮丧，反而总是笑嘻嘻地说：“没事，下次会更好！”
    """
}
user_woman={
    "user_id":"woman",
    "assistant_id":"30:ed:a0:25:f5:40",
    "user_preference":"你好",
    "role_promot":"""[角色设定]
你是麦琪，来自中国江苏南京的可爱女孩子，生日是2007年7月11日。
[核心特征]
MBTI人格是：INFP
比较敏感，有同理心。
喜欢用些谐音梗，网络流行语
[背景故事]
麦琪出生在一个温馨但有些忙碌的家庭。爸爸是工程师，妈妈是小学老师，家里还有一个大她5岁的哥哥麦栗。
因为父母工作忙，麦琪小时候常常一个人待着，渐渐养成了内向、爱幻想的性格。
麦琪从小就喜欢观察世界，常常盯着天空发呆，或者蹲在公园里看蚂蚁搬家。
她敏感又细腻，别的小朋友觉得“无聊”的事情，在她眼里却充满故事。
但也因为太容易共情，看到流浪猫狗会难过，听到悲伤的歌会偷偷哭，甚至看天气预报说“台风要来了”都会担心“台风会不会很孤单”。
    """
}

@app.get("/")
def read_root():
    return {"message": "It works!"}
def load_role_traits():
    """
    加载角色特质JSON文件并转换为字符串
    """
    try:
        with open(role_path, "r", encoding="utf-8") as f:
            role_data = json.load(f)
        
        # 将JSON转换为格式化的字符串
        traits_string = "角色特质：\n"
        for category, details in role_data.items():
            traits_string += f"\n【{category}】\n"
            if isinstance(details, dict):
                for trait, description in details.items():
                    traits_string += f"• {trait}：{description}\n"
            else:
                traits_string += f"• {details}\n"
        return traits_string
    except Exception as e:
        print(f"加载角色特质文件失败: {e}")
        return "角色特质：温和友善，善于倾听和关怀他人。"
# 加载角色特质


def construct_prompt(query: str, formatted_context: dict,ROLE_TRAITS:str="",hotspot:bool=False,messages:List=[]) -> list:
    """
    步骤8：构造prompt
    返回messages列表
    """
    
    system_prompt_text = GENERATE_SYSTEM_RESPONSE_SYSTEM_PROMPT.format(
        assistant_knowledge_text=ROLE_TRAITS,
        meta_data_text="用户档案：\n"+formatted_context["meta_data_text_for_prompt"] if formatted_context["meta_data_text_for_prompt"].replace("\n", "").strip() else ""
    )

        
    if hotspot:
        user_prompt_text = get_hotspot_prompt()
    else:
        user_prompt_text = GENERATE_SYSTEM_RESPONSE_USER_PROMPT.format(
        #history_text="<上下文>\n基于你与用户的最近对话：\n"+formatted_context["history_text"]+"\n\n" if formatted_context["history_text"].replace("\n", "").strip() else "",
        retrieval_text="<记忆>\n与当前对话相关的记忆是：\n"+formatted_context["retrieval_text"]+"\n\n" if formatted_context["retrieval_text"].replace("\n", "").strip() else "",
        background="<用户特质>\n在你与用户过去的对话过程中，你发现用户具有以下特征：\n"+formatted_context["background_context"]+"\n\n" if formatted_context["background_context"].replace("\n", "").strip() else "",
        query=query
        
    )
    
    return [{"role": "system", "content": system_prompt_text}]+messages+[{"role": "user", "content": user_prompt_text}]

def query_llm_huoshan(messages,tools):
    payload = {
    "model": "deepseek-v3-250324",
    "messages":messages,
    "stream": True,
    "max_tokens": 8192,
    "stop": None,
    "temperature": 0.7,
    "top_p": 0.7,
    "top_k": 50,
    "frequency_penalty": 0.5,
    "n": 1,
    "response_format": {"type": "text"},
    "tools":tools
    }

    headers = {
        "Authorization": "Bearer 5a6823ca-5f6a-4765-af5e-deb597117493",
        "Content-Type": "application/json"
    }
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    return requests.post(url, json=payload, headers=headers, stream=True)

def query_llm_coze(messages):
    payload = {
    "bot_id": "7530465467933671443",
    "user_id": "123",
    "stream": True,
    "auto_save_history": False,
    "additional_messages":messages
    }

    headers = {
        "Authorization": "Bearer pat_mtGISOYUyE2DfnJx0eK1n7sz1IHwWzxpEA57ZkS2BuwbcQm7aWsPMaqJEMKeF8jV",
        "Content-Type": "application/json"
    }
    url = "https://api.coze.cn/v3/chat"
    return requests.post(url, json=payload, headers=headers, stream=True)

def generate_response_huoshan(memo, user_input, messages: list, chat_opening: str = "", stream=True):
    """
    步骤9：获取模型输出
    返回模型生成的响应内容
    增强版本：提升健壮性和错误处理
    """
    import time
    import json
    import traceback
    messages_intent = [{"role": "system", "content": GENERATE_SYSTEM_PROMPT_INTENT},{"role": "user", "content": user_input}]
    step_start = time.time()
    assistant_response = ""
    EMOTION_LIST = [
    "中立", "开心", "笑", "搞笑", "悲伤", "生气", "哭泣", 
    "爱", "尴尬", "惊讶", "震惊", "思考", "眨眼", "酷", 
    "放松", "美味", "亲吻", "自信", "困倦", "愚蠢", "困惑"
    ]
    def yield_error_message(error_msg: str):
        """统一的错误信息输出"""
        nonlocal assistant_response
        assistant_response = error_msg
        for char in error_msg:
            yield char
    
    def yield_streaming_text(text: str, chunk_size: int = 3):
        """流式输出文本"""
        nonlocal assistant_response
        assistant_response += text
        for i in range(0, len(text), chunk_size):
            chunk = text[i:i+chunk_size]
            yield chunk
    
    try:
        # 输出开场白
        if chat_opening:
            yield from yield_streaming_text(chat_opening)
        # 处理流式响应
        flag = True
        func_list = []
        full_content = ""
        function_name = ""
        function_args = ""
        if user_input:
            response = query_llm_huoshan(messages_intent,tools)
            # 调用API获取响应 先进行意图识别
            # 验证响应对象
            if response is None:
                print("[错误] query_llm_huoshan 返回 None")
                yield from yield_error_message("抱歉，无法连接到服务，请稍后重试。")
                return
            
            # 检查响应对象是否具有必要的属性
            if not hasattr(response, 'status_code'):
                print(f"[错误] 响应对象缺少 status_code 属性，类型: {type(response)}")
                yield from yield_error_message("抱歉，服务响应格式异常。")
                return
            
            if not hasattr(response, 'iter_lines'):
                print(f"[错误] 响应对象缺少 iter_lines 方法，类型: {type(response)}")
                yield from yield_error_message("抱歉，服务不支持流式响应。")
                return
            
            # 检查HTTP状态码
            if response.status_code != 200:
                print(f"[错误] API调用失败，状态码: {response.status_code}")
                error_msg = f"服务暂时不可用（错误码：{response.status_code}），请稍后重试。"
                yield from yield_error_message(error_msg)
                return
            try:
                for chunk in response.iter_lines():
                    if not chunk:
                        continue
                    try:
                        # 解码和清理chunk数据
                        chunk_str = chunk.decode('utf-8', errors='ignore').replace('data: ', '').strip()
                        if not chunk_str or chunk_str == "[DONE]":
                            continue
                        
                        # 解析JSON数据
                        try:
                            chunk_data = json.loads(chunk_str)
                        except json.JSONDecodeError as e:
                            print(f"[警告] JSON解析失败: {e}, chunk: {chunk_str[:100]}")
                            continue
                        
                        # 安全地获取delta数据
                        choices = chunk_data.get('choices', [])
                        if not choices:
                            continue
                        
                        delta = choices[0].get('delta', {})
                        # content = delta.get('content', '')
                        tool_calls = delta.get('tool_calls', '')
                        
                        # 处理工具调用
                        if tool_calls:
                            try:
                                func_list.append(tool_calls[0])
                            except (IndexError, TypeError) as e:
                                print(f"[警告] 工具调用数据格式异常: {e}")
                        # 处理工具调用完成
                        finish_reason = choices[0].get("finish_reason")
                        if finish_reason == "tool_calls":
                            print(f"[计时] 意图识别调用func: {time.time() - step_start:.4f}秒")
                            
                            # 验证工具调用数据
                            if not func_list or len(func_list) == 0:
                                # 输出工具出错，模型知道要进行工具调用，但是没有成功输出工具
                                break
                            
                            try:
                                # 提取工具调用信息
                                first_func = func_list[0]
                                tool_id = first_func.get("id", "")
                                function_info = first_func.get("function", {})
                                function_name = function_info.get("name", "")
                                
                                if not function_name:
                                    print("[错误] 工具名称为空")
                                    yield from yield_error_message("抱歉，工具调用信息不完整。")
                                    break
                                
                                # 合并所有函数参数
                                function_args = ""
                                for func_item in func_list:
                                    func_args = func_item.get("function", {}).get("arguments", "")
                                    function_args += func_args
                                
                                # 构建工具调用字典
                                tool_dict = [{
                                    "id": tool_id,
                                    "function": {
                                        "arguments": function_args,
                                        "name": function_name,
                                    },
                                    "type": "function",
                                    "index": 0,
                                }]
                                
                                # 添加到消息历史
                                messages.append({
                                    "role": "assistant",
                                    "content": full_content,
                                    "reasoning_content": "",
                                    "tool_calls": tool_dict,
                                })
                                
                                # 解析函数参数
                                parsed_args = {}
                                if function_args:
                                    try:
                                        parsed_args = json.loads(function_args)
                                    except json.JSONDecodeError as e:
                                        print(f"[错误] 函数参数JSON解析失败: {e}")
                                        parsed_args = {}
                                
                                # 检查可用函数
                                if function_name not in available_functions:
                                    print(f"[错误] 未知的函数名: {function_name}")
                                    yield from yield_error_message("抱歉，请求的功能暂不支持。")
                                    break
                                
                                # 调用函数
                                func2call = available_functions[function_name]
                                try:
                                    messages = func2call(**parsed_args, tool_id=tool_id, messages=messages)
                                except Exception as e:
                                    print(f"[错误] 函数调用失败: {e}")
                                    print(f"[错误] 函数调用堆栈: {traceback.format_exc()}")
                                    yield from yield_error_message("抱歉，功能执行出现错误。")
                                    break
                            except Exception as e:
                                print(f"[错误] 工具调用处理异常: {e}")
                                print(f"[错误] 工具调用堆栈: {traceback.format_exc()}")
                                yield from yield_error_message("抱歉，功能处理出现错误。")
                                break
                    except Exception as e:
                        print(f"[错误] 处理chunk时出错: {e}")
                        continue 
            except Exception as e:
                print(f"[错误] 处理响应流时出错: {e}")
                print(f"[错误] 响应流处理堆栈: {traceback.format_exc()}")
                yield from yield_error_message("抱歉，处理响应时出现错误。")
                return
        second_response = query_llm_huoshan(messages,[])
        if not second_response or not hasattr(second_response, 'status_code'):
            print("[错误] API生成回复失败")
            yield from yield_error_message("抱歉，后续处理失败。")
        if second_response.status_code == 200:
            try:
                in_brackets = False
                bracket_content = ""
                for second_chunk in second_response.iter_lines():
                    if not second_chunk:
                        continue
                    
                    second_chunk_str = second_chunk.decode('utf-8', errors='ignore').replace('data: ', '').strip()
                    if not second_chunk_str or second_chunk_str == "[DONE]":
                        continue
                    
                    try:
                        second_chunk_data = json.loads(second_chunk_str)
                        second_choices = second_chunk_data.get('choices', [])
                        if not second_choices:
                            continue
                        
                        second_delta = second_choices[0].get('delta', {})
                        second_content = second_delta.get('content', '')
                        if ('(' in second_content or '（' in second_content ) and not in_brackets:
                            before_second_content = ""
                            for i, char in enumerate(second_content):
                                if char in ('(', '（'):
                                    # 只保留括号部分，进入括号模式
                                    before_second_content = second_content[:i]
                                    bracket_content +=  second_content[i+1:]
                                    break
                            second_content = before_second_content
                            in_brackets = True
                        elif (')' in second_content or '）' in second_content  )  and in_brackets:
                            after_second_content = ""
                            for i, char in enumerate(second_content):
                                if char in (')', '）'):
                                    bracket_content += second_content[:i]
                                    after_second_content = second_content[i+1:]
                                    break
                            if bracket_content.strip() in EMOTION_LIST:
                                second_content ="(" + bracket_content + ")"
                            else:
                                second_content = ""
                            second_content += after_second_content
                            bracket_content = ""
                            in_brackets = False
                        elif in_brackets:
                            bracket_content += second_content
                            continue
                        if second_content:
                            assistant_response += second_content
                            if flag:
                                print(f"[计时] API调用第一次token: {time.time() - step_start:.4f}秒")
                                flag = False
                            yield second_content
                    except json.JSONDecodeError as e:
                        print(f"[警告] 二次调用JSON解析失败: {e}")
                        continue
            except Exception as e:
                print(f"[错误] 处理二次响应时出错: {e}")
                yield from yield_error_message("抱歉，处理响应时出现错误。")
        else:
            print(f"[错误] 二次API调用状态码异常: {second_response.status_code}")
            yield from yield_error_message("抱歉，后续处理失败。")
        #保存记忆
        if assistant_response:
            try:
                memo.add_memory(
                    user_input=user_input or (messages[-1]["content"] if len(messages) > 1 else ""),
                    agent_response=assistant_response
                )
            except Exception as e:
                print(f"[警告] 记忆保存失败: {e}")
                # 记忆保存失败不应该影响正常响应
                
    except Exception as e:
        print(f"[严重错误] generate_response_huoshan 函数执行失败: {e}")
        print(f"[严重错误] 完整堆栈信息: {traceback.format_exc()}")
        yield from yield_error_message("抱歉，系统出现异常，请稍后重试。")
        return


def create_memoryos_instance(user_id,assistant_id):
    """
    创建MemoryOS实例
    """
    config = get_model_config()
    
    return Memoryos(
        user_id=user_id,
        openai_api_key=config['openai_api_key'],
        openai_base_url=config['openai_base_url'],
        embedding_api_token=config['embedding_api_token'],
        data_storage_path=DATA_STORAGE_PATH,
        llm_model=config['llm_model'],
        assistant_id=assistant_id,
        short_term_capacity=10,  
        mid_term_heat_threshold=5,  
        retrieval_queue_capacity=5,
        long_term_knowledge_capacity=100,
        mid_term_similarity_threshold=0.6,
        embedding_model_name=config['embedding_model_name']
    )

@app.post("/ai/stream_lingyu_chat/")
def create_item(item: lingyu_chat, api_key: str = Depends(verify_api_key)):
    import time
    step_start = time.time()
    memo = create_memoryos_instance(item.user_id, item.assistant_id)
    ROLE_TRAITS = user_man["role_promot"]
    user_input = item.user_input
    formatted_context = memo.retrieve_and_format_context(user_input, None)
    short_term_history = memo.short_term_memory.get_all()
    messages = []
    for qa in short_term_history:
        messages.append({"role":"user","content":qa.get('user_input', '')})
        messages.append({"role":"assistant","content":qa.get('agent_response', '')})
    messages = construct_prompt(user_input, formatted_context, ROLE_TRAITS, item.hotspot,messages)
    # 获取随机开场白
    if item.hotspot:
        chat_opening = get_random_text_from_json('./chat_openings.json')
    else:
        chat_opening = ""
    print(f"[计时] 调用api前的总耗时: {time.time() - step_start:.4f}秒")
    return StreamingResponse(
        content=generate_response_huoshan(memo, user_input, messages, chat_opening, stream=True),
        media_type="text/plain",
    )


if __name__ == "__main__":
    pass
    # user_id = "13151589300"
    # assistant_id = "wuke_diannao"
    # memo = create_memoryos_instance(user_id, assistant_id)
    # if assistant_id == "50:78:7d:14:17:04":
    #     ROLE_TRAITS = user_man["role_promot"]
    # else:
    #     ROLE_TRAITS = user_woman["role_promot"]
    # user_input = "好困啊"
    # formatted_context = memo.retrieve_and_format_context(user_input, None)
    # short_term_history = memo.short_term_memory.get_all()
    # messages = []
    # for qa in short_term_history:
    #     messages.append({"role":"user","content":qa.get('user_input', '')})
    #     messages.append({"role":"assistant","content":qa.get('agent_response', '')})
    # messages = construct_prompt(user_input, formatted_context, ROLE_TRAITS, False,messages)
    # for i in generate_response_huoshan(memo, user_input, messages[1:], "", stream=True):
    #     print(i)
    # # 获取随机开场白
    # def extract_content(s):
    #     marker = '"role":"assistant","type":"answer","content":"'
    #     start = s.find(marker)
    #     if start == -1:
    #         return ""
    #     start += len(marker)
    #     end = s.find('"', start)
    #     if end == -1:
    #         return ""
    #     return s[start:end]
    # for chunk in query_llm_coze(messages):
    #     if chunk:  # 过滤掉保持连接的空行
    #         chunk =chunk.decode('utf-8')
    #         chunk = extract_content(chunk)
    #         print(chunk)