#---------------#
# 适配mid_term的操作
#---------------#

from pymongo import MongoClient
from pymongo.errors import PyMongoError
from config.database_config import MONGODB_ADDRESS

import sys
import os
import logging


# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# 创建数据库和集合（在没有数据库的情况下）
def create_database(db_name:str=None, collection_name:str="default") -> bool:
    """
    input:
        db_name:数据库名称
        collection_name: 表名称
    """
    # 连接client
    try:
        client = MongoClient(MONGODB_ADDRESS)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False

    # 判定数据完整性
    if db_name == None or collection_name == None:
        logging.info("⚠️ 没有给出要创建的数据库名称")
        return False
    
    # 获取所有已有的数据库名称
    db_list = client.list_database_names()

    # 检查数据库是否存在
    if db_name in db_list:
        logging.info(f"数据库 [{db_name}] 已存在。")
    else:
        logging.info(f"数据库 [{db_name}] 不存在，正在创建...")
    
    try:
        # 获取数据库对象（即使数据库不存在，也可以操作）
        db = client[db_name]

        # 获取集合对象
        collection = db[collection_name]

        # 插入一个测试文档以创建数据库和集合
        test_doc = {"_id": "init_document", "created": True}
        collection.insert_one(test_doc)

        # 立即删除测试文档
        collection.delete_one({"_id": "init_document"})
        logging.info("✅ 数据库和表创建成功")
        return True
    except Exception as e:
        logging.info("⚠️ 数据库和表创建失败", e)
        return False
    
# 向数据库中插入数据(中期、长期、Assistant等所有的以json形式存储的数据)
def save_JSON_to_mongodb(user_id:str=None, data:dict=None ,db_name:str="ai_emotion", collection_name:str="mid_term_record") -> bool:
    """
    input:
        user_id: 用户id
        data:要插入的数据
        db_name:数据库名称
        collection_name: 表名称
    """
    # 判定数据完整性
    if user_id == None or data == None:
        logging.info("⚠️ 用户名或要保存的数据信息缺失，无法保存相关数据")
        return False

    # 连接client
    try:
        client = MongoClient(MONGODB_ADDRESS)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False
    
    document = {
    "_id": user_id,  # 使用 _id 字段作为主键
    "content": data  # 将整个数据结构作为 content 字段存储
    }

    # 插入文档
    try:
        db = client[db_name]  # 替换为你自己的数据库名
        collection = db[collection_name]  # 替换为你自己的集合名
        collection.insert_one(document)
        logging.info(f"数据已成功插入，User_id: {user_id}")
        return True
    except Exception as e:
        logging.info(f"插入失败：{e}")
        return False
    
# 从数据库中读取user对应的数据
def load_JSON_from_mongodb(user_id:str=None ,db_name:str="ai_emotion", collection_name:str="mid_term_record") -> dict:
    """
    input:
        user_id: 用户id
        db_name:数据库名称
        collection_name: 表名称
    return:
        data:该用户对应的数据
    """
    # 判定数据完整性
    if user_id == None:
        logging.info("⚠️ 用户名缺失，无法保存相关数据")
        return {}
    
    # 连接client
    try:
        client = MongoClient(MONGODB_ADDRESS)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return {}
    
    try:
        db = client[db_name]
        collection = db[collection_name]

        # 查询文档
        result = collection.find_one({"_id": user_id})

        if result:
            print(f"找到用户 [{user_id}] 的数据。")
            return result['content']
        else:
            print(f"未找到用户 [{user_id}] 的数据。")
            return {}
    except Exception as e:
        logging.info("⚠️ 查询数据库失败", e)
        return {}


# 修改user_id对应的数据
def modify_JSON_of_mongodb(user_id:str=None, new_data:dict=None ,db_name:str="ai_emotion", collection_name:str="mid_term_record", upsert:bool=False) -> bool:
    """
    input:
        user_id: 用户id
        new_data:要修改的数据
        db_name:数据库名称
        collection_name: 表名称
        upsert:用户不存在时是不是要插入一条新文档
    """
    # 判定数据完整性
    if user_id == None or new_data == None:
        logging.info("⚠️ 用户名或要保存的数据信息缺失，无法保存相关数据")
        return False

    # 连接client
    try:
        client = MongoClient(MONGODB_ADDRESS)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False
    

    try:
        db = client[db_name]
        collection = db[collection_name]
        result = collection.update_one(
            {"_id": user_id},         # 查询条件
            {"$set": {"content": new_data}},    # 更新操作
            upsert=upsert             # 是否插入新文档
        )

        if result.matched_count > 0:
            logging.info(f"用户 [{user_id}] 数据已更新。")
            return True
        elif result.upserted_id:
            logging.info(f"用户 [{user_id}] 不存在，已插入新文档，ID 为 {result.upserted_id}。")
            return True
        else:
            logging.info(f"未找到用户 [{user_id}]，且未启用 upsert 插入功能。")
            return False
    except PyMongoError as e:
        logging.info(f"数据库操作失败：{e}")
        return False


# 删除对应数据
def delete_JSON_from_mongodb(user_id:str=None ,db_name:str="ai_emotion", collection_name:str="mid_term_record") -> bool:
    """
    input:
        user_id: 用户id
        db_name:数据库名称
        collection_name: 表名称
    """
    # 判定数据完整性
    if user_id == None:
        logging.info("⚠️ 用户名缺失，无法保存相关数据")
        return {}
    
    # 连接client
    try:
        client = MongoClient(MONGODB_ADDRESS)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return {}

    try:
        db = client[db_name]
        collection = db[collection_name]
        result = collection.delete_one({"_id": user_id})

        if result.deleted_count == 1:
            logging.info(f"用户 [{user_id}] 已成功删除。")
            return True
        else:
            logging.info(f"未找到用户 [{user_id}]，删除失败。")
            return False

    except PyMongoError as e:
        logging.info(f"数据库删除失败：{e}")
        return False