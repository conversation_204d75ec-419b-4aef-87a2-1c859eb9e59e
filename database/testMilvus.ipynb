{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a47bb573", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from milvusOperation import create_collection_with_partitions, create_partition_if_not_exists, insert_session_embeddings_by_user, delete_session_embeddings_by_user, search_similar_sessions, drop_collection\n", "from milvusOperation import create_collection_page_with_partitions, insert_page_embeddings_by_user, delete_page_embeddings_by_user, search_similar_pages\n", "from milvusOperation import create_long_collection_with_partitions, create_long_knowledge_collection_with_partitions, insert_long_term_by_user, retrieve_longterm_by_user, search_long_term_knowledges"]}, {"cell_type": "markdown", "id": "36adf1a2", "metadata": {}, "source": ["## 测试长期记忆"]}, {"cell_type": "code", "execution_count": 2, "id": "8ffef68e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-23 10:16:13,052 - INFO - ✅ Delete collection 'long_term_record' with schema.\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["drop_collection(collection_name=\"long_term_record\")"]}, {"cell_type": "code", "execution_count": 3, "id": "d1732e21", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-23 10:17:07,985 - INFO - ✅ Created collection 'long_term_record' with schema.\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["create_long_collection_with_partitions()"]}, {"cell_type": "code", "execution_count": 4, "id": "f2e58653", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collection long_term_usr_knowledge_record already exists.\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["create_long_knowledge_collection_with_partitions()"]}, {"cell_type": "code", "execution_count": 5, "id": "943a7885", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\"/root/wuke/LingYuChatBot/data/memory/users/2/long_term.json\", encoding=\"utf-8\") as file:\n", "    data_to_insert = json.load(file)"]}, {"cell_type": "code", "execution_count": 6, "id": "2e430608", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-23 10:30:03,361 - INFO - 📌 Inserted user 'test_user_001'\n", "2025-07-23 10:30:03,831 - INFO - 🔄 Updated knowledge:long_term_usr_knowledge_record\n", "2025-07-23 10:30:03,840 - INFO - 📌 Inserted knowledge:long_term_usr_knowledge_record\n", "2025-07-23 10:30:04,229 - INFO - 🔄 Updated knowledge:long_term_usr_knowledge_record\n", "2025-07-23 10:30:04,234 - INFO - 📌 Inserted knowledge:long_term_usr_knowledge_record\n", "2025-07-23 10:30:04,630 - INFO - 🔄 Updated knowledge:long_term_usr_knowledge_record\n", "2025-07-23 10:30:04,634 - INFO - 📌 Inserted knowledge:long_term_usr_knowledge_record\n", "2025-07-23 10:30:05,062 - INFO - 🔄 Updated knowledge:long_term_usr_knowledge_record\n", "2025-07-23 10:30:05,066 - INFO - 📌 Inserted knowledge:long_term_usr_knowledge_record\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["insert_long_term_by_user(data=data_to_insert)"]}, {"cell_type": "code", "execution_count": 7, "id": "bffca4ae", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'test_user_001': {'data': '**Updated User Profile:**  \\nThe user exhibits high levels of Openness, Conscientiousness, Science Interest, Tech Interest, Education Interest, Need for Achievement, Need for Knowledge, and Practicality, reflecting a deeply driven and methodical approach to learning and innovation. Their Openness is evident in their enthusiasm for exploring machine learning, natural language processing (NLP), and advanced AI systems, with a willingness to engage with complex, novel concepts. Conscientiousness is demonstrated through structured learning practices, such as prioritizing Python as their primary tool and dedicating time to master technical skills systematically. A strong Science Interest aligns with their focus on programming, machine learning, and NLP, underscoring a passion for scientific and technical domains. Their Tech Interest is further emphasized by a clear preference for practical applications, particularly in NLP, and a project-driven mindset that values real-world relevance. The user’s Education Interest is highlighted by their explicit commitment to learning machine learning, showing a focus on acquiring expertise through targeted study. Their Need for Achievement and Need for Knowledge are both high, driven by a desire to excel in specialized fields and refine their capabilities through in-depth understanding. They balance structured methodologies (e.g., maintaining dialogue state and context memory) with flexibility, ensuring systematic thinking while remaining adaptable. This profile aligns with self-actualization, as their pursuit of personal growth and expertise in AI development reflects a commitment to mastering complex tasks and contributing meaningfully to technological advancement.',\n", "  'last_updated': '2025-06-29 11:46:39'}}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["retrieve_longterm_by_user(user_id=\"test_user_001\")"]}, {"cell_type": "code", "execution_count": 13, "id": "07566ea2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Found top 4 similar sessions in partition 'test_user_001':\n"]}, {"data": {"text/plain": ["[{'output': {'knowledge': '- Likes programming',\n", "   'timestamp': '2025-06-29 11:46:39',\n", "   'knowledge_embedding': [0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    ...]},\n", "  'distance': 0.40959927439689636},\n", " {'output': {'knowledge': '- Learning machine learning',\n", "   'timestamp': '2025-06-29 11:46:39',\n", "   'knowledge_embedding': [0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    ...]},\n", "  'distance': 0.40959927439689636},\n", " {'output': {'knowledge': '- Interested in natural language processing',\n", "   'timestamp': '2025-06-29 11:46:40',\n", "   'knowledge_embedding': [0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    ...]},\n", "  'distance': 0.40959927439689636},\n", " {'output': {'knowledge': '- Uses Python frequently',\n", "   'timestamp': '2025-06-29 11:46:40',\n", "   'knowledge_embedding': [0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    0.7599999904632568,\n", "    ...]},\n", "  'distance': 0.40959927439689636}]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["search_long_term_knowledges(user_id=\"test_user_001\", query_embedding=[0.74]*1024, top_k=30)"]}, {"cell_type": "markdown", "id": "e8bc44a2", "metadata": {}, "source": ["## 测试中期记忆"]}, {"cell_type": "code", "execution_count": 8, "id": "110cdcc7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-23 10:38:42,484 - INFO - ✅ Delete collection 'mid_term_record' with schema.\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["drop_collection()"]}, {"cell_type": "code", "execution_count": 9, "id": "1b954781", "metadata": {}, "outputs": [], "source": ["from pymongo import MongoClient\n", "\n", "import json\n", "\n", "with open(\"/root/wuke/LingYuChatBot/data/memory/users/2/mid_term.json\", encoding=\"utf-8\") as file:\n", "    data_to_insert = json.load(file)"]}, {"cell_type": "code", "execution_count": 10, "id": "17b638be", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-23 10:41:12,459 - INFO - ✅ Created collection 'mid_term_record' with schema.\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["create_collection_with_partitions()"]}, {"cell_type": "code", "execution_count": 11, "id": "bab9833c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-23 10:41:22,558 - INFO - ⚠️ 没有给出用户id，将数据放置到默认分区!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-23 10:41:22,794 - INFO - ✅ Created partition 'default'\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["create_partition_if_not_exists()"]}, {"cell_type": "code", "execution_count": 12, "id": "eb1e60d8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-23 10:41:45,972 - INFO - 📌 Inserted session 'session_a2865c37' into partition 'default'\n", "2025-07-23 10:41:45,982 - INFO - 📌 Inserted session 'session_49c122c1' into partition 'default'\n", "2025-07-23 10:41:45,989 - INFO - 📌 Inserted session 'session_0ac4d10b' into partition 'default'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-23 10:41:45,998 - INFO - 📌 Inserted session 'session_650b7843' into partition 'default'\n", "2025-07-23 10:41:46,007 - INFO - 📌 Inserted session 'session_663bc43c' into partition 'default'\n", "2025-07-23 10:41:46,014 - INFO - 📌 Inserted session 'session_7bd094bd' into partition 'default'\n", "2025-07-23 10:41:46,019 - INFO - 📌 Inserted session 'session_bdce06eb' into partition 'default'\n", "2025-07-23 10:41:46,025 - INFO - 📌 Inserted session 'session_ece1a812' into partition 'default'\n", "2025-07-23 10:41:46,036 - INFO - 📌 Inserted session 'session_c5dd398d' into partition 'default'\n", "2025-07-23 10:41:46,043 - INFO - 📌 Inserted session 'session_566c59c8' into partition 'default'\n", "2025-07-23 10:41:46,051 - INFO - 📌 Inserted session 'session_623c8e67' into partition 'default'\n", "2025-07-23 10:41:46,061 - INFO - 📌 Inserted session 'session_3f559fc2' into partition 'default'\n", "2025-07-23 10:41:46,068 - INFO - 📌 Inserted session 'session_ead26588' into partition 'default'\n", "2025-07-23 10:41:46,076 - INFO - 📌 Inserted session 'session_0ce42213' into partition 'default'\n", "2025-07-23 10:41:46,085 - INFO - 📌 Inserted session 'session_cac6cec4' into partition 'default'\n", "2025-07-23 10:41:46,093 - INFO - 📌 Inserted session 'session_4676398f' into partition 'default'\n", "2025-07-23 10:41:46,101 - INFO - 📌 Inserted session 'session_9cfe1668' into partition 'default'\n", "2025-07-23 10:41:46,115 - INFO - 📌 Inserted session 'session_ab4a0f29' into partition 'default'\n", "2025-07-23 10:41:46,123 - INFO - 📌 Inserted session 'session_ec63feb3' into partition 'default'\n", "2025-07-23 10:41:46,129 - INFO - 📌 Inserted session 'session_7ff70bb3' into partition 'default'\n", "2025-07-23 10:41:46,136 - INFO - 📌 Inserted session 'session_897f9bec' into partition 'default'\n", "2025-07-23 10:41:46,142 - INFO - 📌 Inserted session 'session_5fa7f22a' into partition 'default'\n", "2025-07-23 10:41:46,148 - INFO - 📌 Inserted session 'session_0b7eb729' into partition 'default'\n", "2025-07-23 10:41:46,154 - INFO - 📌 Inserted session 'session_de7aaf02' into partition 'default'\n", "2025-07-23 10:41:46,162 - INFO - 📌 Inserted session 'session_a4e196b5' into partition 'default'\n", "2025-07-23 10:41:46,168 - INFO - 📌 Inserted session 'session_e2a926ad' into partition 'default'\n", "2025-07-23 10:41:46,174 - INFO - 📌 Inserted session 'session_3d5219e0' into partition 'default'\n", "2025-07-23 10:41:46,181 - INFO - 📌 Inserted session 'session_6f26bf60' into partition 'default'\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["insert_session_embeddings_by_user(user_id=\"default\", data=data_to_insert)"]}, {"cell_type": "code", "execution_count": 19, "id": "d6614856", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-22 19:09:39,159 - INFO - 🔄 Deleted session 'session_a2865c37' in partition 'default'\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["delete_session_embeddings_by_user(user_id=\"default\", session_ids=[\"session_a2865c37\"], clear_all=False)"]}, {"cell_type": "code", "execution_count": 13, "id": "0b31689a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Found top 5 similar sessions in partition 'default':\n"]}, {"data": {"text/plain": ["[{'output': {'id': 'session_a2865c37',\n", "   'session_id': 'session_a2865c37',\n", "   'summary': 'General conversation segment from short-term memory.',\n", "   'summary_keywords': '[]',\n", "   'summary_embedding': [-0.05056203901767731,\n", "    -0.039268992841243744,\n", "    -0.010020437650382519,\n", "    -0.05017704889178276,\n", "    0.03430689871311188,\n", "    0.0025117932818830013,\n", "    0.009036574512720108,\n", "    -0.04023147001862526,\n", "    -0.08298676460981369,\n", "    0.031226977705955505,\n", "    0.03659545257687569,\n", "    -0.04619881883263588,\n", "    0.033536918461322784,\n", "    -0.011496233753859997,\n", "    -0.05013427138328552,\n", "    0.07528696209192276,\n", "    -0.067330501973629,\n", "    0.005224102176725864,\n", "    0.09273985028266907,\n", "    -0.04106561467051506,\n", "    0.035782694816589355,\n", "    0.03856318071484566,\n", "    -0.0358896367251873,\n", "    0.09907079488039017,\n", "    -0.027441242709755898,\n", "    3.895435293088667e-05,\n", "    -0.03539770469069481,\n", "    0.016543881967663765,\n", "    0.04589937999844551,\n", "    -0.013816868886351585,\n", "    -0.014244635589420795,\n", "    0.011806364171206951,\n", "    -0.029580075293779373,\n", "    -0.03197557106614113,\n", "    -0.04825209826231003,\n", "    -0.016180280596017838,\n", "    -0.024404097348451614,\n", "    -0.06784381717443466,\n", "    0.0071276649832725525,\n", "    0.011068467050790787,\n", "    -0.03976092487573624,\n", "    0.018458139151334763,\n", "    0.012608427554368973,\n", "    -0.010453552007675171,\n", "    0.036124907433986664,\n", "    -0.06292449682950974,\n", "    0.026649873703718185,\n", "    -0.024874640628695488,\n", "    0.050348155200481415,\n", "    0.011795669794082642,\n", "    -0.03430689871311188,\n", "    0.0011857161298394203,\n", "    -0.021120987832546234,\n", "    -0.018971458077430725,\n", "    -0.02152736485004425,\n", "    -0.02679959125816822,\n", "    0.0056358277797698975,\n", "    0.012608427554368973,\n", "    0.014372965320944786,\n", "    -0.01951686106622219,\n", "    -0.024446874856948853,\n", "    0.04701157286763191,\n", "    -0.028959814459085464,\n", "    0.0394401028752327,\n", "    -0.0022778583224862814,\n", "    0.048080991953611374,\n", "    -0.017656076699495316,\n", "    -0.062026191502809525,\n", "    -0.06373725831508636,\n", "    -0.019687967374920845,\n", "    -0.044017206877470016,\n", "    -0.035526033490896225,\n", "    -0.06848546862602234,\n", "    -0.013624373823404312,\n", "    0.020126428455114365,\n", "    0.01991254650056362,\n", "    -0.01923881284892559,\n", "    0.0029729793313890696,\n", "    0.032895270735025406,\n", "    0.08140403032302856,\n", "    -0.018511610105633736,\n", "    0.0929965078830719,\n", "    0.019206730648875237,\n", "    0.01898215338587761,\n", "    -0.005419271066784859,\n", "    0.05702131986618042,\n", "    -0.031013095751404762,\n", "    -0.018672021105885506,\n", "    0.002125466475263238,\n", "    0.029601464048027992,\n", "    -0.022885525599122047,\n", "    0.028959814459085464,\n", "    -0.03629601374268532,\n", "    -0.05676465854048729,\n", "    -0.0159984789788723,\n", "    -0.024917418137192726,\n", "    0.02964424155652523,\n", "    -0.0052588582038879395,\n", "    -0.019484778866171837,\n", "    0.010646047070622444,\n", "    -0.009052615612745285,\n", "    -0.022586088627576828,\n", "    -0.06630385667085648,\n", "    0.02307802066206932,\n", "    -0.05757741630077362,\n", "    0.0637800320982933,\n", "    -0.06219729781150818,\n", "    -0.0008094150689430535,\n", "    -0.012597733177244663,\n", "    -0.024061884731054306,\n", "    0.01880035176873207,\n", "    0.022949689999222755,\n", "    0.034328289330005646,\n", "    -0.005892488174140453,\n", "    -0.006699897814542055,\n", "    0.016201669350266457,\n", "    0.027441242709755898,\n", "    -0.04782433062791824,\n", "    -0.01766677014529705,\n", "    0.002129476750269532,\n", "    0.012180660851299763,\n", "    0.02397632971405983,\n", "    0.00764098484069109,\n", "    -0.012608427554368973,\n", "    -0.04559994488954544,\n", "    0.007469878066331148,\n", "    -0.02647876739501953,\n", "    0.009191639721393585,\n", "    0.02990090101957321,\n", "    -0.03544048219919205,\n", "    -0.003962190356105566,\n", "    -0.038092635571956635,\n", "    -0.017206920310854912,\n", "    -0.09427981078624725,\n", "    0.012308990582823753,\n", "    0.009213028475642204,\n", "    -0.007983198389410973,\n", "    0.011656645685434341,\n", "    0.04671213775873184,\n", "    -0.017196226865053177,\n", "    -0.07314812391996384,\n", "    0.015410300344228745,\n", "    0.007844174280762672,\n", "    -0.018768269568681717,\n", "    0.02540935017168522,\n", "    0.005183999426662922,\n", "    0.002890099538490176,\n", "    -0.02457520365715027,\n", "    0.017698852345347404,\n", "    -0.015495853498578072,\n", "    0.031825851649045944,\n", "    -0.005416597239673138,\n", "    0.05941681191325188,\n", "    0.007801397703588009,\n", "    -0.01671498827636242,\n", "    0.027441242709755898,\n", "    -0.025002971291542053,\n", "    0.0017952838679775596,\n", "    0.03133391961455345,\n", "    0.023249126970767975,\n", "    0.024019107222557068,\n", "    -0.019024929031729698,\n", "    -0.0021829474717378616,\n", "    -0.009640795178711414,\n", "    -0.005528886336833239,\n", "    -0.015741819515824318,\n", "    -0.004684046842157841,\n", "    -0.021217234432697296,\n", "    0.03020033799111843,\n", "    0.011988164857029915,\n", "    -0.014811426401138306,\n", "    0.01768815889954567,\n", "    0.018736187368631363,\n", "    -0.026072388514876366,\n", "    0.013292853720486164,\n", "    -0.01977352239191532,\n", "    -0.026029611006379128,\n", "    0.021217234432697296,\n", "    0.01840466819703579,\n", "    0.002775137312710285,\n", "    0.008945673704147339,\n", "    -0.020778773352503777,\n", "    0.014672402292490005,\n", "    -0.005464721005409956,\n", "    0.01154970470815897,\n", "    -0.022757194936275482,\n", "    0.011164714582264423,\n", "    -0.01826564408838749,\n", "    -0.03689488768577576,\n", "    -0.015014615841209888,\n", "    -0.004467489663511515,\n", "    -0.018917987123131752,\n", "    -0.04615604132413864,\n", "    -0.0005069705075584352,\n", "    -0.013046888634562492,\n", "    -0.022607477381825447,\n", "    0.004357874393463135,\n", "    -0.04384610056877136,\n", "    -0.02906675636768341,\n", "    -0.02994367852807045,\n", "    -0.019206730648875237,\n", "    0.03231778368353844,\n", "    0.03717293590307236,\n", "    0.023869389668107033,\n", "    -0.011945388279855251,\n", "    -0.005240143742412329,\n", "    0.015260581858456135,\n", "    -0.014244635589420795,\n", "    -0.0219444390386343,\n", "    -0.004908624105155468,\n", "    0.023270515725016594,\n", "    0.03967537358403206,\n", "    -0.044958293437957764,\n", "    -0.03740821033716202,\n", "    -0.032360561192035675,\n", "    0.07639915496110916,\n", "    -0.03191140666604042,\n", "    0.03708738461136818,\n", "    -0.00800993386656046,\n", "    0.04314028471708298,\n", "    0.026457378640770912,\n", "    0.004956748336553574,\n", "    0.03676655888557434,\n", "    0.007427101489156485,\n", "    0.02164500206708908,\n", "    -0.020190594717860222,\n", "    0.002220377093181014,\n", "    -0.031847238540649414,\n", "    0.034905772656202316,\n", "    0.009271846152842045,\n", "    -0.019153259694576263,\n", "    -0.023484399542212486,\n", "    0.012137883342802525,\n", "    0.03131253272294998,\n", "    0.06412224471569061,\n", "    -0.009507117792963982,\n", "    0.02055419608950615,\n", "    0.003721571294590831,\n", "    0.02998645417392254,\n", "    0.028382329270243645,\n", "    0.004264300689101219,\n", "    -0.002070658840239048,\n", "    -0.0194420013576746,\n", "    0.007908339612185955,\n", "    -0.04200670123100281,\n", "    0.018180090934038162,\n", "    -0.0005587704363279045,\n", "    0.043503887951374054,\n", "    -0.03826374188065529,\n", "    0.044958293437957764,\n", "    -0.008261246606707573,\n", "    -0.09214097261428833,\n", "    -0.009261151775717735,\n", "    0.003809798276051879,\n", "    0.02823260985314846,\n", "    -0.00015113870904315263,\n", "    0.0013113727327436209,\n", "    -0.01211649551987648,\n", "    0.01844744384288788,\n", "    -0.023527175188064575,\n", "    -0.021399036049842834,\n", "    -0.05359918251633644,\n", "    -0.016800541430711746,\n", "    9.791849879547954e-05,\n", "    0.07426032423973083,\n", "    0.01411630492657423,\n", "    -0.027034863829612732,\n", "    0.019741438329219818,\n", "    -0.03486299514770508,\n", "    -0.009913496673107147,\n", "    -0.04538606107234955,\n", "    0.012052330188453197,\n", "    0.00020903762197121978,\n", "    0.04880819469690323,\n", "    0.026692649349570274,\n", "    0.006673162337392569,\n", "    0.00814895797520876,\n", "    0.004280341789126396,\n", "    0.012576344422996044,\n", "    0.0948786810040474,\n", "    -0.03133391961455345,\n", "    0.007608902640640736,\n", "    0.018875211477279663,\n", "    -0.009806554764509201,\n", "    0.0013929157285019755,\n", "    0.04384610056877136,\n", "    0.027847619727253914,\n", "    0.00871574878692627,\n", "    0.02848927117884159,\n", "    -0.024040495976805687,\n", "    0.04012452811002731,\n", "    0.015068086795508862,\n", "    -0.05509636551141739,\n", "    0.054326385259628296,\n", "    -0.02039378322660923,\n", "    0.00914351548999548,\n", "    0.02312079630792141,\n", "    0.06279616802930832,\n", "    0.00692447554320097,\n", "    -0.03591102361679077,\n", "    -0.0005611097440123558,\n", "    -0.020800162106752396,\n", "    -0.007967157289385796,\n", "    0.019153259694576263,\n", "    0.052871979773044586,\n", "    0.0007205197471193969,\n", "    -0.01828703097999096,\n", "    -0.017912736162543297,\n", "    0.009496423415839672,\n", "    -9.190302807837725e-05,\n", "    0.07195038348436356,\n", "    -0.01842605695128441,\n", "    0.010993607342243195,\n", "    -0.021024739369750023,\n", "    0.014501295052468777,\n", "    -0.0014664381742477417,\n", "    -0.05030537769198418,\n", "    0.03475605323910713,\n", "    -0.03424273431301117,\n", "    -0.0259440578520298,\n", "    0.03306637704372406,\n", "    0.026992086321115494,\n", "    -0.004684046842157841,\n", "    0.011164714582264423,\n", "    0.00040270236786454916,\n", "    -0.004668005276471376,\n", "    -0.03020033799111843,\n", "    0.025238242000341415,\n", "    0.030991706997156143,\n", "    -0.01623375155031681,\n", "    -0.005999429617077112,\n", "    -0.0028125669341534376,\n", "    0.004734843969345093,\n", "    0.0031387391500175,\n", "    0.03770764544606209,\n", "    0.009250457398593426,\n", "    0.06296727806329727,\n", "    0.02314218506217003,\n", "    0.034093014895915985,\n", "    -0.008977755904197693,\n", "    -0.005523539148271084,\n", "    0.00814895797520876,\n", "    -0.016084032133221626,\n", "    -0.05329974740743637,\n", "    -0.027783455327153206,\n", "    -0.04309750720858574,\n", "    -0.015367522835731506,\n", "    0.030970318242907524,\n", "    -0.002642796840518713,\n", "    -0.013816868886351585,\n", "    -0.02598683536052704,\n", "    -0.006079636048525572,\n", "    0.0016348713543266058,\n", "    -0.020981963723897934,\n", "    -0.021773330867290497,\n", "    -0.026307659223675728,\n", "    -0.015827372670173645,\n", "    0.029430357739329338,\n", "    -0.009822595864534378,\n", "    0.006004776805639267,\n", "    0.020703913643956184,\n", "    -0.0593312606215477,\n", "    0.02707763947546482,\n", "    -0.011421374045312405,\n", "    -0.01226621400564909,\n", "    0.001936981687322259,\n", "    -0.0194420013576746,\n", "    0.017452886328101158,\n", "    0.09291095286607742,\n", "    0.03702322021126747,\n", "    -0.06519166380167007,\n", "    -0.021056821569800377,\n", "    -0.00010660751286195591,\n", "    -0.02062905579805374,\n", "    0.07669859379529953,\n", "    -0.022757194936275482,\n", "    0.07738301903009415,\n", "    0.015431688167154789,\n", "    0.02192305028438568,\n", "    0.06643218547105789,\n", "    0.006673162337392569,\n", "    0.01210580114275217,\n", "    0.007026070263236761,\n", "    0.017816487699747086,\n", "    0.03828513249754906,\n", "    -0.02395494282245636,\n", "    0.009245110675692558,\n", "    -0.013506737537682056,\n", "    -0.014362270943820477,\n", "    0.0052267760038375854,\n", "    0.002563927322626114,\n", "    0.001137592364102602,\n", "    0.028745930641889572,\n", "    -0.02821122296154499,\n", "    0.013635067269206047,\n", "    0.05851850286126137,\n", "    -0.12473680824041367,\n", "    -0.015036003664135933,\n", "    -0.03364386036992073,\n", "    0.004534328356385231,\n", "    0.011453457176685333,\n", "    -0.029152309522032738,\n", "    -0.053470853716135025,\n", "    -0.02908814512193203,\n", "    -0.013528126291930676,\n", "    -0.06420779973268509,\n", "    -0.022051379084587097,\n", "    -0.0018300400115549564,\n", "    0.014479907229542732,\n", "    -0.018297726288437843,\n", "    0.03383635729551315,\n", "    -0.0056892987340688705,\n", "    -0.030307279899716377,\n", "    -0.03398607298731804,\n", "    0.017303168773651123,\n", "    -0.022308040410280228,\n", "    0.08268732577562332,\n", "    0.013806174509227276,\n", "    0.046669360250234604,\n", "    -0.005448679905384779,\n", "    -0.007753273937851191,\n", "    0.057748522609472275,\n", "    -0.0339432992041111,\n", "    -0.04070201516151428,\n", "    -0.005178652238100767,\n", "    0.01824425533413887,\n", "    -0.0222224872559309,\n", "    -0.008860120549798012,\n", "    0.0011008311994373798,\n", "    0.03375080227851868,\n", "    0.020864326506853104,\n", "    -0.0328311026096344,\n", "    0.02339884452521801,\n", "    0.0037964305374771357,\n", "    0.027548182755708694,\n", "    -0.05924570560455322,\n", "    0.04957817494869232,\n", "    0.006068941671401262,\n", "    -0.0030612063128501177,\n", "    -0.01313244178891182,\n", "    -0.0016709641786292195,\n", "    0.07323367893695831,\n", "    0.036916278302669525,\n", "    0.007528696209192276,\n", "    0.00494338059797883,\n", "    -0.010223627090454102,\n", "    -0.017773712053894997,\n", "    -0.02968701720237732,\n", "    -0.009303928352892399,\n", "    -0.002264490583911538,\n", "    0.03843484818935394,\n", "    -0.04487273842096329,\n", "    0.019153259694576263,\n", "    0.07661303877830505,\n", "    -0.013463960960507393,\n", "    -0.0336652509868145,\n", "    -0.003914066590368748,\n", "    -0.04790988564491272,\n", "    -0.012501485645771027,\n", "    0.017998289316892624,\n", "    0.005694645922631025,\n", "    0.0033339078072458506,\n", "    0.054625824093818665,\n", "    0.0003036141861230135,\n", "    0.019634496420621872,\n", "    0.03482022136449814,\n", "    0.009843983687460423,\n", "    0.006641080137342215,\n", "    -0.0065555265173316,\n", "    0.012939946725964546,\n", "    0.029237862676382065,\n", "    0.05282920226454735,\n", "    0.009838636964559555,\n", "    0.003440849483013153,\n", "    -0.00959801860153675,\n", "    0.02543073706328869,\n", "    -0.08803441375494003,\n", "    -0.03875567391514778,\n", "    -0.0008087466703727841,\n", "    0.03047838620841503,\n", "    0.024767698720097542,\n", "    0.003996946383267641,\n", "    0.06724494695663452,\n", "    -0.09162765741348267,\n", "    0.06634663790464401,\n", "    -0.040659237653017044,\n", "    -0.01239454373717308,\n", "    -0.02654293179512024,\n", "    -0.044658858329057693,\n", "    -0.02138834074139595,\n", "    0.03458494693040848,\n", "    0.040937285870313644,\n", "    -0.056037455797195435,\n", "    -0.03715154901146889,\n", "    -0.014212552458047867,\n", "    0.002577295061200857,\n", "    -0.03518382087349892,\n", "    -0.019484778866171837,\n", "    -0.09171320497989655,\n", "    0.022457757964730263,\n", "    -0.013955892994999886,\n", "    0.042605575174093246,\n", "    0.011859835125505924,\n", "    0.03340858966112137,\n", "    -0.03225361928343773,\n", "    -0.029558688402175903,\n", "    -0.03597519174218178,\n", "    0.036659616976976395,\n", "    0.01469379011541605,\n", "    -0.015207110904157162,\n", "    0.011453457176685333,\n", "    -0.058304619044065475,\n", "    0.024703534319996834,\n", "    0.047482118010520935,\n", "    0.029323415830731392,\n", "    -0.034627724438905716,\n", "    -0.017399415373802185,\n", "    0.051503125578165054,\n", "    0.008245205506682396,\n", "    -0.0013668487081304193,\n", "    0.011228878982365131,\n", "    0.02152736485004425,\n", "    0.02996506541967392,\n", "    0.005871099885553122,\n", "    -0.004558390472084284,\n", "    0.07815299928188324,\n", "    0.015399605967104435,\n", "    0.006282825488597155,\n", "    0.04284084588289261,\n", "    0.003914066590368748,\n", "    0.044017206877470016,\n", "    0.007523349020630121,\n", "    0.040402576327323914,\n", "    0.024297155439853668,\n", "    0.004138643853366375,\n", "    0.021495282649993896,\n", "    0.009148863144218922,\n", "    -0.04172865301370621,\n", "    0.01138929184526205,\n", "    -0.0126939807087183,\n", "    0.028125667944550514,\n", "    -0.015602795407176018,\n", "    -0.03334442526102066,\n", "    -0.026029611006379128,\n", "    0.050604816526174545,\n", "    -0.04534328356385231,\n", "    -0.05026260390877724,\n", "    0.015945008024573326,\n", "    -0.05056203901767731,\n", "    0.04311889782547951,\n", "    0.03270277380943298,\n", "    -0.02600822225213051,\n", "    0.06313838064670563,\n", "    -0.040894508361816406,\n", "    0.0036279973573982716,\n", "    -0.005855058319866657,\n", "    0.05184533819556236,\n", "    -0.0010707537876442075,\n", "    0.06382281333208084,\n", "    -0.026585707440972328,\n", "    0.022179709747433662,\n", "    0.0031654746271669865,\n", "    0.0015466444892808795,\n", "    -0.02421160228550434,\n", "    -0.03717293590307236,\n", "    -0.013774091377854347,\n", "    0.008052710443735123,\n", "    0.05081870034337044,\n", "    0.01629791595041752,\n", "    0.01951686106622219,\n", "    0.07716913521289825,\n", "    -0.02425437979400158,\n", "    -0.014009363949298859,\n", "    0.017239002510905266,\n", "    0.04790988564491272,\n", "    -0.02342023327946663,\n", "    -0.014886285178363323,\n", "    0.002890099538490176,\n", "    0.006395114120095968,\n", "    -0.020308230072259903,\n", "    -0.009336010552942753,\n", "    -0.010838542133569717,\n", "    0.016041256487369537,\n", "    -0.02092849276959896,\n", "    -0.009918843396008015,\n", "    -0.009918843396008015,\n", "    0.002860690699890256,\n", "    0.007924380712211132,\n", "    -0.0010433499701321125,\n", "    0.04534328356385231,\n", "    0.002632102696225047,\n", "    -0.043482497334480286,\n", "    -0.003398072673007846,\n", "    0.03766486793756485,\n", "    0.03670239448547363,\n", "    -0.011635257862508297,\n", "    -0.007967157289385796,\n", "    -0.007940421812236309,\n", "    0.0007606229046359658,\n", "    -0.0009210354764945805,\n", "    -0.012469403445720673,\n", "    -0.00360660906881094,\n", "    -0.015827372670173645,\n", "    0.010325221344828606,\n", "    -0.04512939974665642,\n", "    0.04014591500163078,\n", "    -0.005753463599830866,\n", "    0.029858125373721123,\n", "    -0.026115164160728455,\n", "    0.07006820291280746,\n", "    0.016682906076312065,\n", "    0.010651393793523312,\n", "    -0.025708787143230438,\n", "    0.003352622501552105,\n", "    0.008726443164050579,\n", "    -0.01527127530425787,\n", "    -0.055994678288698196,\n", "    0.04752489551901817,\n", "    -0.010886665433645248,\n", "    -0.03559020161628723,\n", "    0.003331234212964773,\n", "    -0.005368473473936319,\n", "    -0.01623375155031681,\n", "    0.03760070353746414,\n", "    0.029858125373721123,\n", "    0.006111718714237213,\n", "    -0.016447635367512703,\n", "    -0.0322750061750412,\n", "    -0.04288362339138985,\n", "    -0.04016730561852455,\n", "    -0.025772951543331146,\n", "    -0.019901851192116737,\n", "    -0.030285891145467758,\n", "    -0.008143611252307892,\n", "    -0.009758430533111095,\n", "    -0.009624753147363663,\n", "    -0.008988450281322002,\n", "    -0.008608807809650898,\n", "    0.004643943626433611,\n", "    -0.009699612855911255,\n", "    0.017634687945246696,\n", "    -0.04418831318616867,\n", "    -0.007908339612185955,\n", "    0.005376494489610195,\n", "    0.03334442526102066,\n", "    0.028895650058984756,\n", "    -0.016608046367764473,\n", "    0.055181920528411865,\n", "    0.04512939974665642,\n", "    -0.009950925596058369,\n", "    0.06745883077383041,\n", "    0.007977851666510105,\n", "    0.05629411339759827,\n", "    -0.0011382608208805323,\n", "    -0.004697414580732584,\n", "    0.06429335474967957,\n", "    0.01979490928351879,\n", "    0.031740300357341766,\n", "    0.01110054925084114,\n", "    -0.04048813134431839,\n", "    0.030991706997156143,\n", "    -0.04782433062791824,\n", "    0.01838327944278717,\n", "    -0.05496803671121597,\n", "    -0.007678414694964886,\n", "    -0.023569952696561813,\n", "    0.07306257635354996,\n", "    0.04418831318616867,\n", "    0.018757576122879982,\n", "    -0.04940706863999367,\n", "    -0.008084792643785477,\n", "    -0.011710116639733315,\n", "    -0.024981582537293434,\n", "    -0.02071460895240307,\n", "    0.02071460895240307,\n", "    -0.050604816526174545,\n", "    -0.023762447759509087,\n", "    0.022714419290423393,\n", "    -0.029815347865223885,\n", "    -0.055438581854104996,\n", "    -0.01139998622238636,\n", "    -0.0009183618822135031,\n", "    0.009154209867119789,\n", "    -0.01840466819703579,\n", "    0.07096651941537857,\n", "    0.0038151454646140337,\n", "    -0.04568549618124962,\n", "    -0.007395018823444843,\n", "    0.03276693820953369,\n", "    0.010544451884925365,\n", "    -0.04602770879864693,\n", "    -0.019869768992066383,\n", "    0.0571068711578846,\n", "    -0.009400175884366035,\n", "    -0.05796240642666817,\n", "    0.03680933639407158,\n", "    -0.00563048105686903,\n", "    0.049193184822797775,\n", "    -0.02990090101957321,\n", "    -0.027890397235751152,\n", "    0.0048498064279556274,\n", "    -0.0046546380035579205,\n", "    -0.023035243153572083,\n", "    0.027804844081401825,\n", "    0.01639416441321373,\n", "    0.04675491526722908,\n", "    -0.039204828441143036,\n", "    -0.03655267506837845,\n", "    -0.02395494282245636,\n", "    -0.010405427776277065,\n", "    0.011506927199661732,\n", "    -0.01528196968138218,\n", "    0.002372769173234701,\n", "    -0.0006727301515638828,\n", "    0.02254331111907959,\n", "    0.02339884452521801,\n", "    -0.01692887209355831,\n", "    0.01138929184526205,\n", "    0.019741438329219818,\n", "    0.005061016418039799,\n", "    0.02996506541967392,\n", "    -0.054369162768125534,\n", "    0.04393165186047554,\n", "    0.0474393405020237,\n", "    0.00021171115804463625,\n", "    0.006448585074394941,\n", "    0.07648470997810364,\n", "    0.03879845142364502,\n", "    0.015260581858456135,\n", "    -0.012629815377295017,\n", "    -0.0022029990795999765,\n", "    -0.00721321813762188,\n", "    0.012865087017416954,\n", "    -0.013923809863626957,\n", "    -0.00814895797520876,\n", "    -0.03556881099939346,\n", "    0.07374700158834457,\n", "    0.011934694834053516,\n", "    0.006170536391437054,\n", "    -0.01991254650056362,\n", "    0.02908814512193203,\n", "    -0.01369923260062933,\n", "    -0.03586824983358383,\n", "    -0.015688348561525345,\n", "    0.04684046655893326,\n", "    0.041792817413806915,\n", "    0.01780579425394535,\n", "    -0.0565507747232914,\n", "    0.018896600231528282,\n", "    0.003940802067518234,\n", "    0.05252976715564728,\n", "    -0.014223246835172176,\n", "    0.009437605738639832,\n", "    0.03456356003880501,\n", "    -0.023805223405361176,\n", "    -0.06283894926309586,\n", "    -0.0015733798500150442,\n", "    0.025559067726135254,\n", "    0.034328289330005646,\n", "    0.029259251430630684,\n", "    -0.026050999760627747,\n", "    -0.002462332835420966,\n", "    0.013784785754978657,\n", "    0.029473135247826576,\n", "    0.020383089780807495,\n", "    -0.01455476600676775,\n", "    0.027783455327153206,\n", "    -0.009122127667069435,\n", "    0.00022875500144436955,\n", "    0.005945958662778139,\n", "    -0.006036859471350908,\n", "    -0.0006580256740562618,\n", "    0.014308799989521503,\n", "    0.011432068422436714,\n", "    0.0029489174485206604,\n", "    0.015303358435630798,\n", "    0.009512464515864849,\n", "    -0.04769600182771683,\n", "    -0.029195085167884827,\n", "    0.007758620660752058,\n", "    0.001169006573036313,\n", "    0.0023821264039725065,\n", "    0.0029916942585259676,\n", "    -0.04050951823592186,\n", "    -0.012597733177244663,\n", "    0.029451746493577957,\n", "    0.009368093684315681,\n", "    -0.007400366012006998,\n", "    0.026500154286623,\n", "    0.048380427062511444,\n", "    0.00026518202503211796,\n", "    0.05030537769198418,\n", "    -0.023163573816418648,\n", "    0.01138929184526205,\n", "    -0.02053280733525753,\n", "    0.06219729781150818,\n", "    0.004774947185069323,\n", "    0.030392833054065704,\n", "    -0.024040495976805687,\n", "    0.02763373777270317,\n", "    -0.02600822225213051,\n", "    0.049749281257390976,\n", "    -0.030350055545568466,\n", "    -0.060058463364839554,\n", "    0.017345944419503212,\n", "    -0.005408576689660549,\n", "    -0.008106181398034096,\n", "    0.008699707686901093,\n", "    -0.04671213775873184,\n", "    0.009277192875742912,\n", "    0.05650799721479416,\n", "    0.07712636142969131,\n", "    -0.013570902869105339,\n", "    0.019623802974820137,\n", "    -0.03413579240441322,\n", "    0.010469593107700348,\n", "    -0.01256565097719431,\n", "    0.017880653962492943,\n", "    -0.03796430677175522,\n", "    0.00634699035435915,\n", "    -0.0052267760038375854,\n", "    0.0291095320135355,\n", "    0.02307802066206932,\n", "    0.024682145565748215,\n", "    -0.016009174287319183,\n", "    0.02647876739501953,\n", "    0.030307279899716377,\n", "    0.009357399307191372,\n", "    1.5130058272916358e-06,\n", "    0.013378407806158066,\n", "    0.016704294830560684,\n", "    -0.04713990539312363,\n", "    0.03402885049581528,\n", "    0.008630195632576942,\n", "    0.05937403440475464,\n", "    0.0124159324914217,\n", "    -0.011068467050790787,\n", "    -0.009336010552942753,\n", "    -0.011410679668188095,\n", "    0.009662183001637459,\n", "    0.035247985273599625,\n", "    -0.0020679852459579706,\n", "    -0.023869389668107033,\n", "    0.015110863372683525,\n", "    -0.034392453730106354,\n", "    0.01565626636147499,\n", "    -0.025302408263087273,\n", "    0.018757576122879982,\n", "    -0.038028471171855927,\n", "    0.019495472311973572,\n", "    0.03159058094024658,\n", "    0.07447420805692673,\n", "    -0.04160032421350479,\n", "    0.005111813545227051,\n", "    -0.054026950150728226,\n", "    0.03794291615486145,\n", "    0.006689203903079033,\n", "    0.016201669350266457,\n", "    -0.04440219700336456,\n", "    0.036381568759679794,\n", "    -0.0075019607320427895,\n", "    0.006908434443175793,\n", "    -0.04641269892454147,\n", "    -0.00850721262395382,\n", "    0.022757194936275482,\n", "    0.022992467507719994,\n", "    0.01010599173605442,\n", "    0.04927873983979225,\n", "    -0.041493382304906845,\n", "    -0.0054567004553973675,\n", "    -0.01736733317375183,\n", "    0.0353335402905941,\n", "    0.06301005184650421,\n", "    0.06313838064670563,\n", "    -0.03742959722876549,\n", "    -0.009726348333060741,\n", "    -0.03559020161628723,\n", "    0.004932686220854521,\n", "    0.017313862219452858,\n", "    0.05167423188686371,\n", "    -0.03330164775252342,\n", "    0.026435989886522293,\n", "    -0.017581216990947723,\n", "    0.004646616987884045,\n", "    -0.06412224471569061,\n", "    -0.009191639721393585,\n", "    0.04301195591688156,\n", "    -0.016084032133221626,\n", "    0.023441622033715248,\n", "    -0.08071959763765335,\n", "    0.03054255060851574,\n", "    -0.03794291615486145,\n", "    0.011186102405190468,\n", "    0.021655695512890816,\n", "    -0.0682288110256195,\n", "    -0.014918368309736252,\n", "    0.007940421812236309,\n", "    -0.021056821569800377,\n", "    0.013089665211737156,\n", "    -0.01685401238501072,\n", "    0.004930012859404087,\n", "    -0.03195418044924736,\n", "    0.0019236139487475157,\n", "    0.0008655594429001212,\n", "    -0.045257728546857834,\n", "    -0.010838542133569717,\n", "    0.00349699379876256,\n", "    -0.030435610562562943,\n", "    -0.015057392418384552,\n", "    0.03627462685108185,\n", "    -0.01914256624877453,\n", "    0.02282135933637619,\n", "    0.002591999713331461,\n", "    -0.009715653955936432,\n", "    0.010250362567603588,\n", "    -0.028617599979043007,\n", "    -0.04309750720858574,\n", "    -0.04098006337881088,\n", "    0.02878870815038681,\n", "    -0.005317676346749067,\n", "    0.020639749243855476,\n", "    0.031269755214452744,\n", "    0.024617981165647507,\n", "    -0.021324176341295242,\n", "    -0.0024650064297020435,\n", "    -0.06168397516012192,\n", "    0.007122317794710398,\n", "    0.01798759587109089,\n", "    0.01225551962852478,\n", "    0.04418831318616867,\n", "    0.02427576668560505,\n", "    0.012736757285892963,\n", "    0.01153901033103466,\n", "    0.05154590308666229,\n", "    0.025216855108737946,\n", "    -0.006491361651569605,\n", "    -0.0014410394942387938,\n", "    -0.022885525599122047,\n", "    -0.009742389433085918,\n", "    -0.004873868077993393,\n", "    0.0628817230463028,\n", "    -0.01528196968138218,\n", "    -0.00619727186858654,\n", "    0.015185722149908543,\n", "    0.0259440578520298,\n", "    -0.02136695384979248,\n", "    0.016640130430459976,\n", "    0.007534043397754431,\n", "    0.02421160228550434,\n", "    0.011645952239632607,\n", "    0.01967727392911911,\n", "    -0.01991254650056362,\n", "    0.010571187362074852,\n", "    0.04309750720858574,\n", "    0.01601986773312092,\n", "    -0.07930797338485718,\n", "    -0.01354951411485672,\n", "    0.024617981165647507,\n", "    -0.020147817209362984,\n", "    -0.006491361651569605,\n", "    0.033536918461322784,\n", "    -0.003574526635929942,\n", "    0.03742959722876549,\n", "    0.01060327049344778,\n", "    0.017474275082349777,\n", "    -0.0132500771433115,\n", "    0.011763587594032288,\n", "    0.03845623880624771,\n", "    0.018094535917043686,\n", "    -0.008416312746703625,\n", "    -0.016725683584809303,\n", "    0.022094156593084335,\n", "    -0.03024311549961567,\n", "    0.032659996300935745,\n", "    0.02481047622859478,\n", "    0.01556001789867878,\n", "    -0.014458518475294113,\n", "    -0.0058497111313045025,\n", "    -0.026906533166766167,\n", "    -0.007261341903358698,\n", "    -0.02851065993309021,\n", "    -0.006512749940156937,\n", "    0.040894508361816406,\n", "    0.04010314121842384,\n", "    0.03082060068845749,\n", "    -0.03618907183408737,\n", "    -0.030392833054065704,\n", "    0.05201644450426102,\n", "    -0.010512369684875011,\n", "    -0.04153615981340408,\n", "    0.01993393339216709,\n", "    0.03704460710287094,\n", "    -0.018436750397086143,\n", "    -0.023527175188064575,\n", "    0.01075298897922039,\n", "    0.008405618369579315,\n", "    0.02371967025101185,\n", "    0.010202239267528057,\n", "    -0.0017070570029318333,\n", "    0.019089095294475555,\n", "    ...],\n", "   'details': 'defaultsession_a2865c37',\n", "   'L_interaction': 35.0,\n", "   'R_recency': 1.0,\n", "   'N_visit': 21.0,\n", "   'H_segment': 57.0,\n", "   'timestamp': '2025-07-22 10:38:14',\n", "   'last_visit_time': '2025-07-22 18:50:04',\n", "   'access_count_lfu': 21.0,\n", "   'access_frequency': 21.0},\n", "  'distance': 41.48262023925781},\n", " {'output': {'id': 'session_bdce06eb',\n", "   'session_id': 'session_bdce06eb',\n", "   'summary': '经典民国喜剧电影',\n", "   'summary_keywords': \"['戏台', '陈佩斯']\",\n", "   'summary_embedding': [-0.06226460263133049,\n", "    -0.04358147829771042,\n", "    -0.009566209279000759,\n", "    0.0071325358003377914,\n", "    0.014077865518629551,\n", "    -0.02388744242489338,\n", "    -0.011531868949532509,\n", "    0.020162049680948257,\n", "    -0.0024991955142468214,\n", "    0.02491707354784012,\n", "    0.006589639000594616,\n", "    -0.0620025135576725,\n", "    0.02205282635986805,\n", "    -0.007876678369939327,\n", "    -0.027968525886535645,\n", "    0.0478123240172863,\n", "    0.00926200021058321,\n", "    0.09584929794073105,\n", "    0.10243893414735794,\n", "    0.009687893092632294,\n", "    0.011896919459104538,\n", "    -0.026695527136325836,\n", "    -0.03229297697544098,\n", "    -0.10850439965724945,\n", "    -0.012786146253347397,\n", "    0.027912363409996033,\n", "    0.013413285836577415,\n", "    0.11823909729719162,\n", "    -0.030814051628112793,\n", "    0.02650832198560238,\n", "    0.018739286810159683,\n", "    0.0023049695882946253,\n", "    -0.04942229390144348,\n", "    -0.03588732331991196,\n", "    -0.011999882757663727,\n", "    -0.00997806154191494,\n", "    0.0781770870089531,\n", "    0.027144821360707283,\n", "    -0.03442712128162384,\n", "    0.025946704670786858,\n", "    0.01842103712260723,\n", "    -0.05384034663438797,\n", "    -0.047962091863155365,\n", "    -0.005892298184335232,\n", "    -0.00789071898907423,\n", "    0.009772135876119137,\n", "    0.04747535660862923,\n", "    -0.024486500769853592,\n", "    0.055862169712781906,\n", "    0.015547430142760277,\n", "    0.024374177679419518,\n", "    -0.014995173551142216,\n", "    -0.01941322721540928,\n", "    0.021678416058421135,\n", "    -0.006350952200591564,\n", "    -0.10101617127656937,\n", "    0.015210459940135479,\n", "    -0.007310381159186363,\n", "    -0.010474156588315964,\n", "    0.006655161269009113,\n", "    0.0026255594566464424,\n", "    0.01366601325571537,\n", "    -0.010174627415835857,\n", "    0.008798666298389435,\n", "    0.01194372121244669,\n", "    -0.04118524491786957,\n", "    -0.013619211502373219,\n", "    -0.05773422494530678,\n", "    -0.023775119334459305,\n", "    -0.0038423961959779263,\n", "    -0.009201157838106155,\n", "    -0.00795156043022871,\n", "    0.013104395940899849,\n", "    0.04807441309094429,\n", "    0.017971742898225784,\n", "    -0.03156287595629692,\n", "    -0.01999356411397457,\n", "    -0.02149120904505253,\n", "    0.02570333704352379,\n", "    -0.019637873396277428,\n", "    -0.008695702999830246,\n", "    0.03633661940693855,\n", "    -0.03633661940693855,\n", "    -0.02340070717036724,\n", "    0.026171350851655006,\n", "    0.048973001539707184,\n", "    0.05331617221236229,\n", "    0.0009307631407864392,\n", "    -0.00064936961280182,\n", "    -0.0019995905458927155,\n", "    -0.008723783306777477,\n", "    0.07162488251924515,\n", "    0.00774563429877162,\n", "    -0.06874191761016846,\n", "    -0.002149354899302125,\n", "    0.0019796998240053654,\n", "    0.00246175448410213,\n", "    -0.00419340655207634,\n", "    0.004287009593099356,\n", "    -0.018149588257074356,\n", "    -0.02287653088569641,\n", "    -0.0027963845059275627,\n", "    -0.0340714305639267,\n", "    0.01456460077315569,\n", "    -0.0032152573112398386,\n", "    0.023250943049788475,\n", "    -0.055824726819992065,\n", "    -0.014199549332261086,\n", "    0.004085763357579708,\n", "    -0.019244741648435593,\n", "    0.002037031576037407,\n", "    -0.004764384124428034,\n", "    0.003386082360520959,\n", "    0.023737678304314613,\n", "    -0.007492906413972378,\n", "    -0.021603533998131752,\n", "    0.07125047594308853,\n", "    0.0023587914183735847,\n", "    0.030870214104652405,\n", "    -0.01459268108010292,\n", "    -0.03416503220796585,\n", "    0.021210400387644768,\n", "    -0.04092315584421158,\n", "    -0.05091993883252144,\n", "    0.01922602206468582,\n", "    0.01155994925647974,\n", "    -0.06020534038543701,\n", "    0.006865767762064934,\n", "    0.007385263219475746,\n", "    -0.0032363177742809057,\n", "    0.018570801243185997,\n", "    0.037740662693977356,\n", "    -0.019637873396277428,\n", "    0.006903208792209625,\n", "    0.001044841599650681,\n", "    0.0307766105979681,\n", "    0.011906280182301998,\n", "    0.004177026450634003,\n", "    0.037441130727529526,\n", "    -0.01973147690296173,\n", "    -0.058707695454359055,\n", "    -0.01614648848772049,\n", "    0.011915639974176884,\n", "    -0.013375844806432724,\n", "    0.006687922403216362,\n", "    0.015669113025069237,\n", "    -0.016942111775279045,\n", "    -0.021060636267066002,\n", "    0.0430198609828949,\n", "    0.002698101568967104,\n", "    -9.989176760427654e-05,\n", "    -0.0001242431317223236,\n", "    -0.038489483296871185,\n", "    0.032948195934295654,\n", "    -0.015528709627687931,\n", "    -0.01918858103454113,\n", "    -0.00042794045293703675,\n", "    -0.002097873482853174,\n", "    0.05443940684199333,\n", "    0.022221311926841736,\n", "    -0.022708047181367874,\n", "    -0.020536459982395172,\n", "    -0.008106005378067493,\n", "    -0.00940240453928709,\n", "    0.009744054637849331,\n", "    -0.0017609032802283764,\n", "    -0.017906220629811287,\n", "    -0.006346271838992834,\n", "    0.05410243570804596,\n", "    0.022127708420157433,\n", "    -0.01404042448848486,\n", "    -0.007418024353682995,\n", "    0.01574399508535862,\n", "    -0.0037885745987296104,\n", "    -0.011456985957920551,\n", "    0.007207417860627174,\n", "    -0.013329043053090572,\n", "    0.0022979495115578175,\n", "    -0.017850060015916824,\n", "    0.0053447214886546135,\n", "    0.02362535521388054,\n", "    -0.0037862344179302454,\n", "    -0.0047831046395003796,\n", "    0.001372451544739306,\n", "    0.016867229714989662,\n", "    -0.0218281801789999,\n", "    1.0658291103027295e-05,\n", "    -0.031656477600336075,\n", "    -0.011971801519393921,\n", "    -0.013460086658596992,\n", "    -0.03236785903573036,\n", "    -0.01743820682168007,\n", "    -0.015978002920746803,\n", "    0.011073214933276176,\n", "    -0.038751572370529175,\n", "    0.002331880386918783,\n", "    0.04526633024215698,\n", "    0.01901073567569256,\n", "    -0.019544271752238274,\n", "    -0.013132477179169655,\n", "    -0.00010661947453627363,\n", "    0.012533418834209442,\n", "    0.03128206729888916,\n", "    0.005691051948815584,\n", "    0.01272998470813036,\n", "    -0.014424196444451809,\n", "    0.05515078827738762,\n", "    7.905051461420953e-05,\n", "    0.023063737899065018,\n", "    -0.01628689281642437,\n", "    0.009308801032602787,\n", "    0.02677040919661522,\n", "    -0.029821861535310745,\n", "    -0.04174686223268509,\n", "    -0.02130400389432907,\n", "    0.01259894110262394,\n", "    -0.016230730339884758,\n", "    0.015612952411174774,\n", "    0.008508496917784214,\n", "    0.019806358963251114,\n", "    0.004413373302668333,\n", "    -0.036149412393569946,\n", "    0.04253312572836876,\n", "    0.004656740929931402,\n", "    0.009594290517270565,\n", "    -0.05357826128602028,\n", "    0.019656594842672348,\n", "    0.020293094217777252,\n", "    0.0017433527391403913,\n", "    0.045079123228788376,\n", "    0.02780004031956196,\n", "    0.006444554775953293,\n", "    -0.07016468048095703,\n", "    -0.03332260623574257,\n", "    0.05410243570804596,\n", "    -0.0016345394542440772,\n", "    -0.013029513880610466,\n", "    0.05990581214427948,\n", "    0.03729136660695076,\n", "    -0.05807119607925415,\n", "    -0.007539708167314529,\n", "    0.01496709231287241,\n", "    -0.06035510450601578,\n", "    -0.003589668544009328,\n", "    -0.016174569725990295,\n", "    0.030589405447244644,\n", "    0.005414923653006554,\n", "    0.07974961400032043,\n", "    -0.03723520413041115,\n", "    0.05616169795393944,\n", "    0.016230730339884758,\n", "    0.022015385329723358,\n", "    -0.02203410677611828,\n", "    0.015940561890602112,\n", "    0.03070172853767872,\n", "    0.013581770472228527,\n", "    0.007937519811093807,\n", "    -0.004584198817610741,\n", "    0.0008207798236981034,\n", "    0.03540059179067612,\n", "    0.03021499328315258,\n", "    -0.00622926838696003,\n", "    -0.03712288290262222,\n", "    -0.011372744105756283,\n", "    0.09584929794073105,\n", "    -0.0029952905606478453,\n", "    -0.026995055377483368,\n", "    -0.018514640629291534,\n", "    -0.04732559248805046,\n", "    0.007712873164564371,\n", "    -0.018973292782902718,\n", "    0.019001374021172523,\n", "    0.0059250593185424805,\n", "    -0.028810951858758926,\n", "    -0.0034937255550175905,\n", "    0.12026091665029526,\n", "    0.033191561698913574,\n", "    -0.03204960748553276,\n", "    -0.0034820253495126963,\n", "    -0.012261970899999142,\n", "    -0.037197764962911606,\n", "    -0.029353847727179527,\n", "    0.004528036806732416,\n", "    0.004326791036874056,\n", "    0.042158715426921844,\n", "    0.02701377682387829,\n", "    0.021977944299578667,\n", "    0.02783748134970665,\n", "    0.004024921916425228,\n", "    0.028773510828614235,\n", "    0.018870331346988678,\n", "    0.02386872097849846,\n", "    -0.03410886973142624,\n", "    0.054738935083150864,\n", "    -0.04316962510347366,\n", "    -0.015715915709733963,\n", "    -0.013226079754531384,\n", "    0.05717260763049126,\n", "    0.010221429169178009,\n", "    0.05361570045351982,\n", "    0.053203847259283066,\n", "    -0.021098077297210693,\n", "    -0.015088776126503944,\n", "    -0.04418053478002548,\n", "    0.03313540294766426,\n", "    0.010652001947164536,\n", "    0.05312896519899368,\n", "    -0.013825138099491596,\n", "    -0.01632433384656906,\n", "    0.02023693174123764,\n", "    -0.00564425066113472,\n", "    -0.013057595118880272,\n", "    -0.014517799019813538,\n", "    0.039013657718896866,\n", "    -0.032124489545822144,\n", "    -0.026208791881799698,\n", "    0.011906280182301998,\n", "    0.05803375318646431,\n", "    -0.006219908129423857,\n", "    0.037927865982055664,\n", "    -0.055075906217098236,\n", "    0.0249919556081295,\n", "    0.055824726819992065,\n", "    -0.03070172853767872,\n", "    -0.03568140044808388,\n", "    -0.020442858338356018,\n", "    -0.00895779114216566,\n", "    -0.01380641758441925,\n", "    -0.017166759818792343,\n", "    -0.007488226518034935,\n", "    0.035606518387794495,\n", "    -0.004188726656138897,\n", "    0.0032831192947924137,\n", "    0.02124784328043461,\n", "    0.0053447214886546135,\n", "    0.07226138561964035,\n", "    0.016960833221673965,\n", "    -0.0018135547870770097,\n", "    0.0034024629276245832,\n", "    0.0051107145845890045,\n", "    0.011606751009821892,\n", "    0.04275777190923691,\n", "    -0.03553163260221481,\n", "    -0.061103928834199905,\n", "    -0.009912539273500443,\n", "    -0.023737678304314613,\n", "    -0.004394652787595987,\n", "    -0.009580249898135662,\n", "    0.007829876616597176,\n", "    0.04777488484978676,\n", "    -0.015238541178405285,\n", "    0.007609909866005182,\n", "    0.0014929651515558362,\n", "    -0.013132477179169655,\n", "    -0.02019949071109295,\n", "    0.03513850271701813,\n", "    -0.006458595395088196,\n", "    -0.04335683211684227,\n", "    -0.008199607953429222,\n", "    0.009107555262744427,\n", "    -0.006861087400466204,\n", "    -0.07667943835258484,\n", "    0.028848392888903618,\n", "    0.02360663376748562,\n", "    -0.03965015709400177,\n", "    -0.0225957240909338,\n", "    -0.015238541178405285,\n", "    -0.022240031510591507,\n", "    -0.019656594842672348,\n", "    0.004165325779467821,\n", "    0.02783748134970665,\n", "    -0.04541609436273575,\n", "    0.03231169655919075,\n", "    -0.007516307290643454,\n", "    -0.026321114972233772,\n", "    -0.07585573196411133,\n", "    0.030383478850126266,\n", "    0.024505220353603363,\n", "    0.05226781964302063,\n", "    0.004003860987722874,\n", "    0.004843946546316147,\n", "    -0.006730043329298496,\n", "    -0.0021505251061171293,\n", "    0.00157369754742831,\n", "    0.021191680803894997,\n", "    0.00919179804623127,\n", "    0.04380612447857857,\n", "    0.03603709116578102,\n", "    -0.03264866769313812,\n", "    -0.036374058574438095,\n", "    0.026732968166470528,\n", "    0.02678913064301014,\n", "    -0.04335683211684227,\n", "    -0.0030444320291280746,\n", "    0.028174452483654022,\n", "    0.053728025406599045,\n", "    -0.09577441215515137,\n", "    -0.020087167620658875,\n", "    0.004979670513421297,\n", "    0.0032222773879766464,\n", "    0.007773715071380138,\n", "    0.012187088839709759,\n", "    -0.05339105427265167,\n", "    -0.015996724367141724,\n", "    0.017138678580522537,\n", "    -0.014536519534885883,\n", "    -0.021996665745973587,\n", "    -0.006655161269009113,\n", "    0.02888583391904831,\n", "    -0.00702489260584116,\n", "    0.053952671587467194,\n", "    0.006758124101907015,\n", "    0.003463304601609707,\n", "    0.007188697345554829,\n", "    -0.013497527688741684,\n", "    -0.011822037398815155,\n", "    0.06323806941509247,\n", "    0.006795565597712994,\n", "    0.08566530793905258,\n", "    0.01101705338805914,\n", "    -0.05249246582388878,\n", "    -0.022183870896697044,\n", "    -0.06540966033935547,\n", "    -0.012327493168413639,\n", "    -0.03830227628350258,\n", "    0.02997162565588951,\n", "    -0.025385087355971336,\n", "    0.006622400134801865,\n", "    -0.050283439457416534,\n", "    0.008368092589080334,\n", "    -0.022782929241657257,\n", "    0.023700237274169922,\n", "    0.027219703420996666,\n", "    -0.05256734788417816,\n", "    -0.04646444320678711,\n", "    0.025422528386116028,\n", "    0.05223038047552109,\n", "    0.0006160236080177128,\n", "    -0.04623979702591896,\n", "    -0.012720624916255474,\n", "    -0.08394301682710648,\n", "    0.04219615459442139,\n", "    -0.027126099914312363,\n", "    -0.06836750358343124,\n", "    0.006533477455377579,\n", "    0.005410243757069111,\n", "    0.02682657167315483,\n", "    -0.00918243732303381,\n", "    -0.010343112982809544,\n", "    -0.011138737201690674,\n", "    0.023775119334459305,\n", "    -0.02126656286418438,\n", "    -0.023288384079933167,\n", "    0.04706350341439247,\n", "    0.0006909059011377394,\n", "    -0.04335683211684227,\n", "    0.022651884704828262,\n", "    0.02235235646367073,\n", "    -0.013113756664097309,\n", "    0.03672974929213524,\n", "    -0.046651650220155716,\n", "    -0.003308860119432211,\n", "    -0.020667504519224167,\n", "    -0.014124667271971703,\n", "    -0.005915699061006308,\n", "    0.01324480026960373,\n", "    0.029784420505166054,\n", "    0.013675373047590256,\n", "    -0.054252199828624725,\n", "    0.014751805923879147,\n", "    -0.0005189107032492757,\n", "    0.05062041059136391,\n", "    0.02179073914885521,\n", "    -0.0009360283147543669,\n", "    -0.026077749207615852,\n", "    0.014105946756899357,\n", "    -0.050096236169338226,\n", "    0.030420919880270958,\n", "    0.017045075073838234,\n", "    0.04814929515123367,\n", "    -0.00041331499232910573,\n", "    -0.03618685528635979,\n", "    -0.0016661303816363215,\n", "    -0.06432386487722397,\n", "    -0.011513148434460163,\n", "    -0.05653610825538635,\n", "    0.024224411696195602,\n", "    0.04275777190923691,\n", "    0.02570333704352379,\n", "    -0.02783748134970665,\n", "    0.049272529780864716,\n", "    0.025385087355971336,\n", "    0.036355338990688324,\n", "    0.02340070717036724,\n", "    -0.01598736271262169,\n", "    -0.014817328192293644,\n", "    -0.0052698394283652306,\n", "    -0.02025565318763256,\n", "    -0.05432708188891411,\n", "    -0.01590312086045742,\n", "    0.027126099914312363,\n", "    -0.03968759998679161,\n", "    -0.022726766765117645,\n", "    -0.022932693362236023,\n", "    -0.009538128040730953,\n", "    -0.022165149450302124,\n", "    -0.04077339172363281,\n", "    0.012954631820321083,\n", "    -0.029484892264008522,\n", "    0.029035598039627075,\n", "    0.023531751707196236,\n", "    -0.04287009686231613,\n", "    0.018411677330732346,\n", "    -0.019806358963251114,\n", "    -0.01782197877764702,\n", "    0.024767309427261353,\n", "    0.03287331387400627,\n", "    -0.013319682329893112,\n", "    -0.014864129014313221,\n", "    -0.016483457759022713,\n", "    -0.0023693216498941183,\n", "    0.026339836418628693,\n", "    -0.0084055345505476,\n", "    0.03476409241557121,\n", "    0.05380290746688843,\n", "    0.03100125677883625,\n", "    0.015201099216938019,\n", "    -0.04829905927181244,\n", "    0.0005268084350973368,\n", "    0.03341621160507202,\n", "    0.03075788915157318,\n", "    0.041147805750370026,\n", "    -0.024299293756484985,\n", "    0.0071325358003377914,\n", "    -0.00729634054005146,\n", "    0.005251118913292885,\n", "    -0.011719074100255966,\n", "    0.050545528531074524,\n", "    0.03938807174563408,\n", "    0.005976540502160788,\n", "    -0.01745692826807499,\n", "    -0.02263316512107849,\n", "    -0.006697282660752535,\n", "    -0.014817328192293644,\n", "    0.009318161755800247,\n", "    -0.026096468791365623,\n", "    -0.010062304325401783,\n", "    -0.05777166783809662,\n", "    0.048486266285181046,\n", "    0.02965337596833706,\n", "    0.026059027761220932,\n", "    -0.020143328234553337,\n", "    0.01626817137002945,\n", "    0.005611489526927471,\n", "    -0.01328224129974842,\n", "    -0.005634890403598547,\n", "    0.0023985726293176413,\n", "    -0.008087284862995148,\n", "    0.02517916075885296,\n", "    0.016885951161384583,\n", "    0.011101295240223408,\n", "    -0.02416825108230114,\n", "    -0.001189925940707326,\n", "    -0.018992014229297638,\n", "    0.027444349601864815,\n", "    -0.021397607401013374,\n", "    0.05357826128602028,\n", "    -0.00032205224852077663,\n", "    0.02521660178899765,\n", "    -0.0008406704291701317,\n", "    0.03395910561084747,\n", "    -0.059269312769174576,\n", "    -0.0357937216758728,\n", "    -0.001581887830980122,\n", "    0.06840495020151138,\n", "    -0.06630824506282806,\n", "    -0.003924298565834761,\n", "    0.021154239773750305,\n", "    0.06630824506282806,\n", "    -0.01537894457578659,\n", "    -0.03150671347975731,\n", "    -0.00038113901973702013,\n", "    -0.004539737477898598,\n", "    -0.04238336160778999,\n", "    0.012542779557406902,\n", "    -0.0012495977571234107,\n", "    0.03047708235681057,\n", "    0.012570859864354134,\n", "    -0.008045163005590439,\n", "    -0.030364757403731346,\n", "    -0.00505923293530941,\n", "    -0.04313218593597412,\n", "    0.05694796144962311,\n", "    -0.02336326614022255,\n", "    0.01731652393937111,\n", "    -0.05282943695783615,\n", "    -0.019637873396277428,\n", "    0.03968759998679161,\n", "    -0.01782197877764702,\n", "    0.005101354327052832,\n", "    0.02916664257645607,\n", "    0.017709655687212944,\n", "    0.01785941980779171,\n", "    0.020068446174263954,\n", "    -0.0340714305639267,\n", "    0.04440518096089363,\n", "    -0.04313218593597412,\n", "    -0.011372744105756283,\n", "    0.04395588859915733,\n", "    -0.006898528430610895,\n", "    -0.015669113025069237,\n", "    0.02648960053920746,\n", "    -0.00039810454472899437,\n", "    -0.047400474548339844,\n", "    0.009196477942168713,\n", "    0.0038002748042345047,\n", "    -0.03528826683759689,\n", "    0.0011156287509948015,\n", "    0.0034726650919765234,\n", "    -0.08199607580900192,\n", "    0.01614648848772049,\n", "    0.011737794615328312,\n", "    0.06196507439017296,\n", "    -0.03461432829499245,\n", "    -0.008644221350550652,\n", "    0.020929593592882156,\n", "    -0.011588030494749546,\n", "    -0.030121389776468277,\n", "    0.004336151294410229,\n", "    -0.003423523623496294,\n", "    -0.043506596237421036,\n", "    0.01580015756189823,\n", "    -0.026882732287049294,\n", "    0.04597771167755127,\n", "    0.025871822610497475,\n", "    -0.02705121785402298,\n", "    -0.013441366143524647,\n", "    0.002831485588103533,\n", "    -0.016839148476719856,\n", "    -0.04732559248805046,\n", "    0.04253312572836876,\n", "    -0.05047064647078514,\n", "    -0.005232398398220539,\n", "    0.04698862135410309,\n", "    0.012430455535650253,\n", "    -0.023700237274169922,\n", "    -0.03397782891988754,\n", "    0.04893555864691734,\n", "    0.06952818483114243,\n", "    -0.057434696704149246,\n", "    0.055300552397966385,\n", "    -0.03882645443081856,\n", "    0.00960365030914545,\n", "    -0.042420800775289536,\n", "    -0.02942872978746891,\n", "    0.041896626353263855,\n", "    -0.013375844806432724,\n", "    0.01712931878864765,\n", "    0.003297159681096673,\n", "    -0.046876296401023865,\n", "    -0.05807119607925415,\n", "    0.016680024564266205,\n", "    0.01366601325571537,\n", "    -0.07091350108385086,\n", "    -0.06885424256324768,\n", "    -0.021753298118710518,\n", "    0.036617428064346313,\n", "    -0.006210547871887684,\n", "    -0.008311931043863297,\n", "    -0.002981250174343586,\n", "    -0.03603709116578102,\n", "    -0.022445958107709885,\n", "    0.016062244772911072,\n", "    0.014761166647076607,\n", "    -0.007993681356310844,\n", "    -0.04208383336663246,\n", "    -0.07244858890771866,\n", "    0.04081083461642265,\n", "    0.010820487514138222,\n", "    -0.054776377975940704,\n", "    0.042944978922605515,\n", "    0.007549068424850702,\n", "    0.003554567461833358,\n", "    -0.025927983224391937,\n", "    0.03045836091041565,\n", "    0.031637758016586304,\n", "    -0.041372451931238174,\n", "    -0.003463304601609707,\n", "    0.046389561146497726,\n", "    -0.05204317346215248,\n", "    0.012233889661729336,\n", "    -0.045079123228788376,\n", "    0.020105887204408646,\n", "    0.04129756987094879,\n", "    -0.005555327981710434,\n", "    0.04013689234852791,\n", "    0.006847047246992588,\n", "    -0.0006359142134897411,\n", "    0.012233889661729336,\n", "    -0.01235557347536087,\n", "    -0.029110480099916458,\n", "    -0.004621639847755432,\n", "    0.015435107052326202,\n", "    0.015772076323628426,\n", "    -0.035569075495004654,\n", "    -0.04856114834547043,\n", "    -0.023307105526328087,\n", "    -0.03710416331887245,\n", "    -0.001953958999365568,\n", "    -0.002494515385478735,\n", "    0.07847661525011063,\n", "    -0.003746453206986189,\n", "    0.011747155338525772,\n", "    0.006767484825104475,\n", "    -0.0012226869584992528,\n", "    0.0280621275305748,\n", "    -0.01656770147383213,\n", "    0.023850001394748688,\n", "    -0.021903062239289284,\n", "    -0.008672302588820457,\n", "    -0.003973440267145634,\n", "    -0.0029204082675278187,\n", "    0.029222803190350533,\n", "    0.06278877705335617,\n", "    0.00430339016020298,\n", "    0.01586567983031273,\n", "    0.028230613097548485,\n", "    0.03912598267197609,\n", "    -0.06949073821306229,\n", "    -0.0519682914018631,\n", "    0.02287653088569641,\n", "    0.012748705223202705,\n", "    0.057846549898386,\n", "    -0.0018673765007406473,\n", "    0.04957205802202225,\n", "    -0.040586188435554504,\n", "    0.031038697808980942,\n", "    0.01922602206468582,\n", "    0.052904319018125534,\n", "    -0.015041974373161793,\n", "    -0.018655043095350266,\n", "    -0.040286656469106674,\n", "    0.0015210460405796766,\n", "    0.026096468791365623,\n", "    0.022240031510591507,\n", "    0.04197150841355324,\n", "    -0.002220727037638426,\n", "    -0.020873431116342545,\n", "    0.0010302161099389195,\n", "    0.025890542194247246,\n", "    0.02446777932345867,\n", "    -0.05533799156546593,\n", "    -0.032667387276887894,\n", "    0.0003074267879128456,\n", "    -0.015931202098727226,\n", "    0.01077368576079607,\n", "    0.020124608650803566,\n", "    0.012383654713630676,\n", "    0.047700002789497375,\n", "    -0.005297920200973749,\n", "    -0.026115190237760544,\n", "    -0.030888933688402176,\n", "    0.008686342276632786,\n", "    -0.006037382408976555,\n", "    0.006191827356815338,\n", "    0.018495919182896614,\n", "    0.02841782011091709,\n", "    -0.04945973679423332,\n", "    0.031038697808980942,\n", "    -0.06061719357967377,\n", "    0.010848567821085453,\n", "    -0.021210400387644768,\n", "    0.01993740350008011,\n", "    0.05331617221236229,\n", "    -0.022127708420157433,\n", "    -0.06069207563996315,\n", "    0.05601193383336067,\n", "    0.030102670192718506,\n", "    -0.03570012003183365,\n", "    0.024561382830142975,\n", "    0.027968525886535645,\n", "    0.009130955673754215,\n", "    0.009102875366806984,\n", "    0.003294819500297308,\n", "    0.006584959104657173,\n", "    0.02920408360660076,\n", "    0.007815835997462273,\n", "    0.0325363427400589,\n", "    0.02154737152159214,\n", "    0.07847661525011063,\n", "    -0.016754906624555588,\n", "    -0.0051107145845890045,\n", "    -0.055525198578834534,\n", "    0.017082516103982925,\n", "    -0.04777488484978676,\n", "    0.021903062239289284,\n", "    -0.003397782798856497,\n", "    0.03489513322710991,\n", "    -0.05050808563828468,\n", "    0.002407932886853814,\n", "    0.00028387983911670744,\n", "    0.07742825895547867,\n", "    -0.04852370545268059,\n", "    -0.03334132954478264,\n", "    -0.01775645650923252,\n", "    0.02991546504199505,\n", "    -0.0504332035779953,\n", "    -0.009687893092632294,\n", "    0.03334132954478264,\n", "    -0.004900108091533184,\n", "    -0.00716061657294631,\n", "    -0.014508438296616077,\n", "    -0.0037651737220585346,\n", "    0.0515189990401268,\n", "    -0.016577061265707016,\n", "    0.028455261141061783,\n", "    -0.04421797767281532,\n", "    -0.017531810328364372,\n", "    -0.034782811999320984,\n", "    0.0015385964652523398,\n", "    -0.01538830529898405,\n", "    0.07001491636037827,\n", "    0.05080761760473251,\n", "    0.0689665675163269,\n", "    0.05387778952717781,\n", "    0.025441249832510948,\n", "    0.003851756453514099,\n", "    0.005803375504910946,\n", "    -0.045565858483314514,\n", "    -0.0064726355485618114,\n", "    0.005106034222990274,\n", "    -0.007052973378449678,\n", "    0.026096468791365623,\n", "    0.03510105982422829,\n", "    -0.021116798743605614,\n", "    0.04017433524131775,\n", "    -0.016885951161384583,\n", "    0.022127708420157433,\n", "    -0.0075116269290447235,\n", "    0.028923274949193,\n", "    0.009552168659865856,\n", "    -0.010558399371802807,\n", "    -0.036130692809820175,\n", "    0.02311989851295948,\n", "    -0.03287331387400627,\n", "    -0.02600286528468132,\n", "    -0.04680141434073448,\n", "    0.004153625573962927,\n", "    -0.01525726169347763,\n", "    0.015453827567398548,\n", "    0.04204639047384262,\n", "    0.044554948806762695,\n", "    -0.05762190371751785,\n", "    0.01390002015978098,\n", "    -0.016455378383398056,\n", "    0.023475589230656624,\n", "    0.0005306110251694918,\n", "    0.010530318133533001,\n", "    0.045079123228788376,\n", "    0.00557404849678278,\n", "    0.02018076926469803,\n", "    -0.019244741648435593,\n", "    -0.001007985440082848,\n", "    -0.008686342276632786,\n", "    -0.0006751104374416173,\n", "    0.012233889661729336,\n", "    0.046314679086208344,\n", "    0.0016579400980845094,\n", "    -0.031637758016586304,\n", "    0.021397607401013374,\n", "    0.05436452478170395,\n", "    -0.019282182678580284,\n", "    -0.017709655687212944,\n", "    0.02735074609518051,\n", "    -0.013628572225570679,\n", "    0.056086815893650055,\n", "    0.02677040919661522,\n", "    -0.026133909821510315,\n", "    0.02021821215748787,\n", "    0.08267001807689667,\n", "    0.01653026044368744,\n", "    0.020049726590514183,\n", "    0.03987480700016022,\n", "    -0.0003796764649450779,\n", "    -0.00203118147328496,\n", "    -0.017447566613554955,\n", "    0.06368736922740936,\n", "    -0.054514288902282715,\n", "    -0.027388188987970352,\n", "    0.017653493210673332,\n", "    -0.03386550396680832,\n", "    -0.03199344873428345,\n", "    0.0023073097690939903,\n", "    0.004984350875020027,\n", "    -0.027144821360707283,\n", "    -0.035849884152412415,\n", "    0.02360663376748562,\n", "    0.015013894066214561,\n", "    0.017363324761390686,\n", "    -0.012027963995933533,\n", "    -0.020367976278066635,\n", "    -0.004942229483276606,\n", "    -0.011410185135900974,\n", "    -0.03603709116578102,\n", "    -0.01616520807147026,\n", "    -0.014265071600675583,\n", "    0.05653610825538635,\n", "    0.01712931878864765,\n", "    -0.03738497197628021,\n", "    0.022408517077565193,\n", "    0.004256588872522116,\n", "    0.044480063021183014,\n", "    -0.017222920432686806,\n", "    -0.0556749626994133,\n", "    -0.009641091339290142,\n", "    0.013029513880610466,\n", "    -0.038526926189661026,\n", "    -0.016258811578154564,\n", "    0.009056073613464832,\n", "    -0.057846549898386,\n", "    0.018814168870449066,\n", "    0.0347079299390316,\n", "    -0.01090472936630249,\n", "    0.004315090365707874,\n", "    0.029016876593232155,\n", "    -0.010296311229467392,\n", "    -0.017747096717357635,\n", "    0.025123000144958496,\n", "    -0.03309796005487442,\n", "    -0.002553017111495137,\n", "    -0.004776084329932928,\n", "    -0.06859215348958969,\n", "    0.01882352866232395,\n", "    0.008330651558935642,\n", "    -0.03386550396680832,\n", "    -0.019525550305843353,\n", "    -0.011953081004321575,\n", "    0.006013981997966766,\n", "    0.018655043095350266,\n", "    0.007923479191958904,\n", "    0.022670606151223183,\n", "    0.01207476481795311,\n", "    -0.025853101164102554,\n", "    0.027500512078404427,\n", "    -0.012992072850465775,\n", "    0.013488167896866798,\n", "    0.00983765721321106,\n", "    -0.015435107052326202,\n", "    0.0022593382745981216,\n", "    0.0025459970347583294,\n", "    0.019160499796271324,\n", "    -0.03045836091041565,\n", "    -0.022932693362236023,\n", "    -0.030289875343441963,\n", "    0.06657033413648605,\n", "    -0.03173135966062546,\n", "    -0.008602100424468517,\n", "    0.004731622990220785,\n", "    0.02180945873260498,\n", "    0.0351572223007679,\n", "    0.009538128040730953,\n", "    -0.002299119485542178,\n", "    -0.00873782392591238,\n", "    0.08910989761352539,\n", "    0.02999034710228443,\n", "    -0.023063737899065018,\n", "    -0.011073214933276176,\n", "    0.011007692664861679,\n", "    0.03315412253141403,\n", "    -0.007249539252370596,\n", "    0.04515400528907776,\n", "    0.023194780573248863,\n", "    -0.0022616784553974867,\n", "    0.00950536783784628,\n", "    0.011222979053854942,\n", "    0.021472489461302757,\n", "    0.020892152562737465,\n", "    -0.003297159681096673,\n", "    0.028455261141061783,\n", "    0.02257700264453888,\n", "    -0.009514727629721165,\n", "    0.004998391028493643,\n", "    0.041335009038448334,\n", "    0.012037323787808418,\n", "    0.006973410956561565,\n", "    -0.019600432366132736,\n", "    -0.007801795843988657,\n", "    0.0724860280752182,\n", "    0.07720361649990082,\n", "    -0.002220727037638426,\n", "    0.0020733026321977377,\n", "    -0.013460086658596992,\n", "    0.00550852669402957,\n", "    0.004282329697161913,\n", "    0.05593705177307129,\n", "    0.012346213683485985,\n", "    -0.0270699393004179,\n", "    0.004759703762829304,\n", "    0.015715915709733963,\n", "    0.026751689612865448,\n", "    ...],\n", "   'details': 'defaultsession_bdce06eb',\n", "   'L_interaction': 4.0,\n", "   'R_recency': 1.0,\n", "   'N_visit': 6.0,\n", "   'H_segment': 11.0,\n", "   'timestamp': '2025-07-22 14:11:35',\n", "   'last_visit_time': '2025-07-22 18:42:06',\n", "   'access_count_lfu': 6.0,\n", "   'access_frequency': 6.0},\n", "  'distance': 41.59565734863281},\n", " {'output': {'id': 'session_cac6cec4',\n", "   'session_id': 'session_cac6cec4',\n", "   'summary': '用户表达不清导致AI困惑',\n", "   'summary_keywords': \"['困惑', '没听懂']\",\n", "   'summary_embedding': [0.018864266574382782,\n", "    -0.024493198841810226,\n", "    -0.007973505184054375,\n", "    -0.10118373483419418,\n", "    -0.0260790903121233,\n", "    -0.017082586884498596,\n", "    0.029622869566082954,\n", "    -0.007405717391520739,\n", "    -0.07248107343912125,\n", "    -0.01818879507482052,\n", "    0.024062464013695717,\n", "    -0.0902978703379631,\n", "    -0.00621140468865633,\n", "    -0.009936289861798286,\n", "    -0.06970086693763733,\n", "    0.04706766456365585,\n", "    -0.07447811961174011,\n", "    -0.005976458080112934,\n", "    0.022633204236626625,\n", "    -0.011062076315283775,\n", "    -0.024023305624723434,\n", "    0.010641129687428474,\n", "    -0.021086471155285835,\n", "    -0.01594701036810875,\n", "    -0.04381756857037544,\n", "    0.030856341123580933,\n", "    0.013881437480449677,\n", "    0.09914752840995789,\n", "    0.003081228816881776,\n", "    0.024336567148566246,\n", "    -0.06445372849702835,\n", "    -0.013920594938099384,\n", "    -0.04922134429216385,\n", "    -0.037885162979364395,\n", "    -0.05070934072136879,\n", "    -0.01209975779056549,\n", "    0.012755651026964188,\n", "    -0.054037753492593765,\n", "    0.015907853841781616,\n", "    0.04095904901623726,\n", "    -0.012373862788081169,\n", "    0.0059666684828698635,\n", "    0.09930416196584702,\n", "    0.010729234665632248,\n", "    -0.01984321139752865,\n", "    -0.06578508764505386,\n", "    0.0297990795224905,\n", "    -0.002049665665253997,\n", "    0.03408685699105263,\n", "    -0.010278920643031597,\n", "    -0.043582621961832047,\n", "    -0.007581927347928286,\n", "    -0.01197249535471201,\n", "    0.011815863661468029,\n", "    -0.016886798664927483,\n", "    -0.010014605708420277,\n", "    0.019755106419324875,\n", "    -0.018051741644740105,\n", "    -0.025178460404276848,\n", "    -0.005207986570894718,\n", "    -0.0730292797088623,\n", "    0.0184041615575552,\n", "    -0.05630890652537346,\n", "    -0.0013692990178242326,\n", "    0.014478594064712524,\n", "    0.028017399832606316,\n", "    -0.003631885163486004,\n", "    -0.011081655509769917,\n", "    -0.0212235227227211,\n", "    -0.014459014870226383,\n", "    -0.004720961209386587,\n", "    -0.02010752633213997,\n", "    -0.017023850232362747,\n", "    -0.0033235177397727966,\n", "    0.014165331609547138,\n", "    -0.020381631329655647,\n", "    -0.015966590493917465,\n", "    -0.002547703916206956,\n", "    0.002280941465869546,\n", "    0.039431896060705185,\n", "    -0.022300362586975098,\n", "    0.12319041788578033,\n", "    0.04839903116226196,\n", "    -0.01076839305460453,\n", "    -0.003130176104605198,\n", "    0.0318940207362175,\n", "    0.01543795969337225,\n", "    0.008526609279215336,\n", "    0.04080241918563843,\n", "    0.02735171839594841,\n", "    0.05720953643321991,\n", "    0.08536398410797119,\n", "    -0.036769166588783264,\n", "    -0.026314036920666695,\n", "    0.065941721200943,\n", "    -0.024943513795733452,\n", "    -0.02997528947889805,\n", "    -0.035594433546066284,\n", "    -0.014282804913818836,\n", "    -0.0003279465017840266,\n", "    -0.011864811182022095,\n", "    -0.042251259088516235,\n", "    -0.0164266936480999,\n", "    -0.03304917737841606,\n", "    -0.07075813412666321,\n", "    0.06692066788673401,\n", "    -0.11269612610340118,\n", "    0.013861858285963535,\n", "    0.003688174532726407,\n", "    -0.03792432323098183,\n", "    0.048281557857990265,\n", "    -0.007939242757856846,\n", "    0.053137123584747314,\n", "    0.01324512343853712,\n", "    0.05854089930653572,\n", "    -0.020459946244955063,\n", "    0.01721963845193386,\n", "    -0.001539390766993165,\n", "    -0.0006014392129145563,\n", "    -0.02004878968000412,\n", "    -0.02228078432381153,\n", "    -0.04941713437438011,\n", "    0.024806462228298187,\n", "    -0.0003695516788866371,\n", "    -0.020714472979307175,\n", "    0.008595135062932968,\n", "    -0.04898639768362045,\n", "    -5.154756217962131e-05,\n", "    0.051061760634183884,\n", "    -0.0035927274730056524,\n", "    0.029524974524974823,\n", "    -0.06719477474689484,\n", "    -0.04123315587639809,\n", "    -0.012373862788081169,\n", "    0.010083131492137909,\n", "    -0.026470666751265526,\n", "    0.014948487281799316,\n", "    -0.003029834246262908,\n", "    0.028389399871230125,\n", "    -0.005834511015564203,\n", "    -0.01157112792134285,\n", "    0.0006589522236026824,\n", "    -0.020812366157770157,\n", "    -0.025452565401792526,\n", "    -0.015251959674060345,\n", "    0.011649442836642265,\n", "    0.02249615080654621,\n", "    -0.010964182205498219,\n", "    0.013352807611227036,\n", "    -0.0923340767621994,\n", "    -0.00467935623601079,\n", "    0.010944603011012077,\n", "    -0.006074352655559778,\n", "    -0.003071439452469349,\n", "    -0.00634356215596199,\n", "    -0.023749200627207756,\n", "    0.01690637692809105,\n", "    0.009006292559206486,\n", "    0.032442230731248856,\n", "    -0.020029211416840553,\n", "    0.0029686500784009695,\n", "    0.023631727322936058,\n", "    0.007361664902418852,\n", "    -0.02574624866247177,\n", "    -0.012618598528206348,\n", "    -0.024082042276859283,\n", "    -0.003991647623479366,\n", "    -0.012344494462013245,\n", "    0.024023305624723434,\n", "    0.01701406016945839,\n", "    -0.0172685869038105,\n", "    0.04879060760140419,\n", "    0.0016458509489893913,\n", "    0.007337191142141819,\n", "    0.015124697238206863,\n", "    -0.032500967383384705,\n", "    -0.02094941958785057,\n", "    -0.016084063798189163,\n", "    0.005203091539442539,\n", "    0.019265633076429367,\n", "    0.012755651026964188,\n", "    -0.022006679326295853,\n", "    0.009251028299331665,\n", "    0.016436483711004257,\n", "    0.033832333981990814,\n", "    0.0022711518686264753,\n", "    0.029838237911462784,\n", "    -0.015663117170333862,\n", "    -0.03970600292086601,\n", "    -0.009109081700444221,\n", "    -0.04507061839103699,\n", "    -0.006142878904938698,\n", "    -0.019686579704284668,\n", "    -0.013783542439341545,\n", "    0.0024999803863465786,\n", "    -0.03365612402558327,\n", "    0.005697458982467651,\n", "    -0.018590161576867104,\n", "    0.0009795567020773888,\n", "    -0.031913600862026215,\n", "    -0.023396780714392662,\n", "    0.019686579704284668,\n", "    0.03283380717039108,\n", "    0.019109003245830536,\n", "    -0.026039931923151016,\n", "    -0.0007262546569108963,\n", "    0.015741432085633278,\n", "    -0.01316680759191513,\n", "    -0.05650469288229942,\n", "    0.005056249909102917,\n", "    -0.011394917964935303,\n", "    0.031130444258451462,\n", "    0.025922458618879318,\n", "    -0.023925410583615303,\n", "    -0.017444796860218048,\n", "    0.002422888297587633,\n", "    0.02932918630540371,\n", "    0.005535932723432779,\n", "    0.004564329981803894,\n", "    0.018511846661567688,\n", "    0.01701406016945839,\n", "    -0.0065589300356805325,\n", "    0.006930929142981768,\n", "    -0.020244577899575233,\n", "    0.036064326763153076,\n", "    -0.03911863639950752,\n", "    0.01175712700933218,\n", "    -0.02558961696922779,\n", "    -0.04111568257212639,\n", "    0.029133398085832596,\n", "    -0.014253436587750912,\n", "    -0.03240307420492172,\n", "    0.07694505900144577,\n", "    -0.0017192717641592026,\n", "    0.026470666751265526,\n", "    -0.012706703506410122,\n", "    0.01316680759191513,\n", "    -0.029192134737968445,\n", "    0.0077238744124770164,\n", "    -0.016436483711004257,\n", "    -0.022378677502274513,\n", "    -0.03179612755775452,\n", "    -0.018257319927215576,\n", "    -0.02255488932132721,\n", "    -0.0036000695545226336,\n", "    0.030347289517521858,\n", "    0.02361214905977249,\n", "    0.023964568972587585,\n", "    -0.018756583333015442,\n", "    -0.020342472940683365,\n", "    0.036867059767246246,\n", "    -0.06977918744087219,\n", "    -0.02069489285349846,\n", "    0.07682758569717407,\n", "    0.013264701701700687,\n", "    -0.025844143703579903,\n", "    0.014860382303595543,\n", "    0.034263066947460175,\n", "    0.018971949815750122,\n", "    0.021106049418449402,\n", "    -0.01705321855843067,\n", "    -0.031267497688531876,\n", "    0.01183544285595417,\n", "    -0.0056729852221906185,\n", "    0.0557606965303421,\n", "    0.015173644758760929,\n", "    0.0007134060142561793,\n", "    0.019853001460433006,\n", "    -0.03257928416132927,\n", "    -0.008800714276731014,\n", "    0.05720953643321991,\n", "    0.006113510113209486,\n", "    -0.004314699210226536,\n", "    0.012452177703380585,\n", "    0.02239825762808323,\n", "    0.014488383196294308,\n", "    0.031071707606315613,\n", "    -0.0027434928342700005,\n", "    0.029740342870354652,\n", "    0.004219252150505781,\n", "    -0.03208981081843376,\n", "    -0.0035168591421097517,\n", "    -0.006989665795117617,\n", "    -0.012129126116633415,\n", "    0.04644114151597023,\n", "    0.032128967344760895,\n", "    0.008996502496302128,\n", "    0.062300048768520355,\n", "    0.01076839305460453,\n", "    -0.02143889106810093,\n", "    0.037611059844493866,\n", "    0.014380699023604393,\n", "    -0.06359225511550903,\n", "    0.05305880680680275,\n", "    -0.009016081690788269,\n", "    -0.019755106419324875,\n", "    0.0035266487393528223,\n", "    0.0769842192530632,\n", "    -0.00876645091921091,\n", "    -0.04655861482024193,\n", "    0.016505008563399315,\n", "    -0.013137439265847206,\n", "    0.0039671738632023335,\n", "    -0.08019515872001648,\n", "    0.027743296697735786,\n", "    -0.021615101024508476,\n", "    0.06147773563861847,\n", "    0.014860382303595543,\n", "    0.002515888074412942,\n", "    0.004184989258646965,\n", "    0.09977405518293381,\n", "    -0.023749200627207756,\n", "    0.035163696855306625,\n", "    -0.04338683560490608,\n", "    -0.018276900053024292,\n", "    0.030053606256842613,\n", "    -0.007102244533598423,\n", "    0.07005328685045242,\n", "    0.0029931238386780024,\n", "    -0.02094941958785057,\n", "    -0.008012663573026657,\n", "    -0.029152976348996162,\n", "    -0.036553800106048584,\n", "    0.0184041615575552,\n", "    0.012393441051244736,\n", "    0.002863413654267788,\n", "    -0.021575944498181343,\n", "    0.0383746363222599,\n", "    0.057875216007232666,\n", "    -0.030778024345636368,\n", "    0.039157792925834656,\n", "    -0.041624732315540314,\n", "    0.033832333981990814,\n", "    -0.013450701721012592,\n", "    0.02537424862384796,\n", "    0.05082681402564049,\n", "    0.0812915787100792,\n", "    0.013274491764605045,\n", "    -0.0021634679287672043,\n", "    -0.033401597291231155,\n", "    -0.030993392691016197,\n", "    0.012961229309439659,\n", "    0.008947555907070637,\n", "    0.015428170561790466,\n", "    -0.03514412045478821,\n", "    -0.04174220561981201,\n", "    0.022476572543382645,\n", "    0.033734437078237534,\n", "    -0.021458469331264496,\n", "    0.0053744069300591946,\n", "    -0.029250871390104294,\n", "    0.0317765474319458,\n", "    0.03152202442288399,\n", "    0.021458469331264496,\n", "    -0.04804661124944687,\n", "    -0.016279852017760277,\n", "    -0.02645108848810196,\n", "    0.08544230461120605,\n", "    -0.025785407051444054,\n", "    -0.027488769963383675,\n", "    -0.046950191259384155,\n", "    -0.08747851103544235,\n", "    0.031639497727155685,\n", "    -0.019588686525821686,\n", "    0.0025109935086220503,\n", "    -0.0020680208690464497,\n", "    -0.012364072725176811,\n", "    -0.006235878448933363,\n", "    0.0024277830962091684,\n", "    0.00031096793827600777,\n", "    -0.03976473957300186,\n", "    0.07365580648183823,\n", "    0.023377202451229095,\n", "    0.016397325322031975,\n", "    0.01599595881998539,\n", "    0.09382206946611404,\n", "    0.06394467502832413,\n", "    -0.02521761879324913,\n", "    -0.0006381496204994619,\n", "    0.007797295227646828,\n", "    0.012344494462013245,\n", "    0.003690621815621853,\n", "    -0.022633204236626625,\n", "    -0.030895497649908066,\n", "    0.004158067982643843,\n", "    0.03291212394833565,\n", "    -0.0033675702288746834,\n", "    0.01769932173192501,\n", "    -0.021497627720236778,\n", "    -0.011512391269207,\n", "    0.0006363141001202166,\n", "    -0.008247610181570053,\n", "    0.026137826964259148,\n", "    0.0025256776716560125,\n", "    -0.01034744642674923,\n", "    -0.02911381796002388,\n", "    -0.08434588462114334,\n", "    -0.07823727279901505,\n", "    0.025354670360684395,\n", "    -0.012413020245730877,\n", "    -0.00714629702270031,\n", "    -0.01674974523484707,\n", "    0.0012787466403096914,\n", "    0.016984691843390465,\n", "    -0.05486006662249565,\n", "    -0.024728145450353622,\n", "    -0.017415428534150124,\n", "    0.021673837676644325,\n", "    0.008761555887758732,\n", "    -0.007743453606963158,\n", "    0.002995571121573448,\n", "    0.008844766765832901,\n", "    -0.02447362057864666,\n", "    -0.012227020226418972,\n", "    0.03195275738835335,\n", "    -0.003254991490393877,\n", "    0.11997947841882706,\n", "    0.03195275738835335,\n", "    -0.0028438346926122904,\n", "    0.02196752093732357,\n", "    0.018619529902935028,\n", "    0.023925410583615303,\n", "    -0.029015924781560898,\n", "    -0.02985781617462635,\n", "    0.01274586096405983,\n", "    0.0026455982588231564,\n", "    -0.012892703525722027,\n", "    -0.01833563670516014,\n", "    -0.030249394476413727,\n", "    0.008653871715068817,\n", "    -0.016886798664927483,\n", "    -0.033244967460632324,\n", "    -0.04354346543550491,\n", "    -0.011228497140109539,\n", "    -0.08136989176273346,\n", "    -0.027958663180470467,\n", "    0.07596611976623535,\n", "    0.0538419634103775,\n", "    0.006074352655559778,\n", "    -0.026118246838450432,\n", "    -0.03056265600025654,\n", "    0.0220654159784317,\n", "    0.0012304112315177917,\n", "    -0.03128707781434059,\n", "    -0.03600559011101723,\n", "    0.019383106380701065,\n", "    -0.04467904195189476,\n", "    -0.03808095306158066,\n", "    0.002229546895250678,\n", "    0.030151499435305595,\n", "    0.011062076315283775,\n", "    -0.005599564407020807,\n", "    0.024728145450353622,\n", "    0.054311856627464294,\n", "    0.02862434647977352,\n", "    -0.05219733715057373,\n", "    -0.028448136523365974,\n", "    -0.0018489820649847388,\n", "    -0.0022173100151121616,\n", "    0.006852613762021065,\n", "    0.015751222148537636,\n", "    0.056113116443157196,\n", "    -0.03396938368678093,\n", "    -0.006769403349608183,\n", "    0.017102165147662163,\n", "    0.03498748689889908,\n", "    0.04072410240769386,\n", "    -0.03455675020813942,\n", "    -0.01669100858271122,\n", "    -8.841095404932275e-05,\n", "    0.016808481886982918,\n", "    0.03747400641441345,\n", "    0.03349949046969414,\n", "    0.015085539780557156,\n", "    0.009828605689108372,\n", "    -0.03514412045478821,\n", "    -0.043269362300634384,\n", "    0.022006679326295853,\n", "    -0.03438054025173187,\n", "    0.06057710573077202,\n", "    0.04185967892408371,\n", "    0.0405283160507679,\n", "    -0.018276900053024292,\n", "    -0.013205965049564838,\n", "    0.040606629103422165,\n", "    -0.04957376420497894,\n", "    0.011943127028644085,\n", "    0.009750289842486382,\n", "    -0.047968294471502304,\n", "    0.0027165717910975218,\n", "    -0.018688056617975235,\n", "    -0.010083131492137909,\n", "    0.018785951659083366,\n", "    -0.03279465064406395,\n", "    0.004383225459605455,\n", "    -0.0482032410800457,\n", "    -0.004730750806629658,\n", "    -0.03389107063412666,\n", "    -0.04879060760140419,\n", "    -0.02063615620136261,\n", "    0.007699401117861271,\n", "    0.003171781077980995,\n", "    0.019853001460433006,\n", "    0.033186230808496475,\n", "    -0.0288984514772892,\n", "    -0.05458596348762512,\n", "    -0.028115294873714447,\n", "    -0.004882487002760172,\n", "    0.027234245091676712,\n", "    0.007518296130001545,\n", "    0.029505396261811256,\n", "    -0.03994094952940941,\n", "    0.007860926911234856,\n", "    -0.013284280896186829,\n", "    -0.02010752633213997,\n", "    -0.008923081681132317,\n", "    -0.038472529500722885,\n", "    0.022378677502274513,\n", "    0.009206975810229778,\n", "    -0.03173739090561867,\n", "    0.04824240133166313,\n", "    -0.0002656917495187372,\n", "    0.017444796860218048,\n", "    0.0023604806046932936,\n", "    0.04471819847822189,\n", "    0.07306843996047974,\n", "    0.006005826406180859,\n", "    -0.08920145034790039,\n", "    0.04788998141884804,\n", "    0.012530493550002575,\n", "    0.022907309234142303,\n", "    -0.016769325360655785,\n", "    0.003453227924183011,\n", "    0.05027860403060913,\n", "    0.012922071851789951,\n", "    0.005007302854210138,\n", "    0.017885321751236916,\n", "    0.018952371552586555,\n", "    -0.026509825140237808,\n", "    -0.015604380518198013,\n", "    0.014038068242371082,\n", "    0.0060254051350057125,\n", "    0.03774811327457428,\n", "    -0.0375327430665493,\n", "    0.009182502515614033,\n", "    -0.057444483041763306,\n", "    -0.02997528947889805,\n", "    -0.06143857538700104,\n", "    0.04276030883193016,\n", "    0.035379067063331604,\n", "    0.07475222647190094,\n", "    -0.012716492637991905,\n", "    0.0518057607114315,\n", "    -0.04757671803236008,\n", "    0.006749824620783329,\n", "    -0.014987644739449024,\n", "    0.00808118935674429,\n", "    0.049495451152324677,\n", "    -0.0017107060411944985,\n", "    -0.008624503389000893,\n", "    -0.02836981974542141,\n", "    -0.042251259088516235,\n", "    0.00434651505202055,\n", "    0.007351875305175781,\n", "    0.003303938778117299,\n", "    0.013274491764605045,\n", "    0.005888353101909161,\n", "    0.03684748336672783,\n", "    0.050670184195041656,\n", "    -0.03238349407911301,\n", "    0.0005264887586236,\n", "    -0.02543298527598381,\n", "    -0.009657290764153004,\n", "    0.0021010602358728647,\n", "    0.03475254029035568,\n", "    -0.03923610970377922,\n", "    0.005359722767025232,\n", "    0.015212802216410637,\n", "    -0.032775070518255234,\n", "    -0.020087948068976402,\n", "    -0.0196670014411211,\n", "    0.0708756074309349,\n", "    0.019794264808297157,\n", "    -0.01781679503619671,\n", "    0.023044360801577568,\n", "    -0.0220654159784317,\n", "    0.029309608042240143,\n", "    -0.004248620476573706,\n", "    0.008179083466529846,\n", "    -0.007200139109045267,\n", "    0.012775229290127754,\n", "    -0.02147804945707321,\n", "    -0.015075749717652798,\n", "    -0.011394917964935303,\n", "    -0.009314659982919693,\n", "    0.002738598035648465,\n", "    0.028702661395072937,\n", "    0.003989200107753277,\n", "    -0.020871102809906006,\n", "    -0.0007966163102537394,\n", "    0.04420914873480797,\n", "    0.06104699894785881,\n", "    -0.008761555887758732,\n", "    -0.03056265600025654,\n", "    -0.00513456529006362,\n", "    0.020753629505634308,\n", "    -0.0030200446490198374,\n", "    -0.02175215445458889,\n", "    0.040175896137952805,\n", "    0.01972573809325695,\n", "    -0.00821334682404995,\n", "    0.015770800411701202,\n", "    -0.03248138725757599,\n", "    -0.013117860071361065,\n", "    0.02762582339346409,\n", "    0.004561882931739092,\n", "    -0.033147070556879044,\n", "    0.022202467545866966,\n", "    0.0039353580214083195,\n", "    -0.050043657422065735,\n", "    -0.009769869036972523,\n", "    -0.011463443748652935,\n", "    0.05454680323600769,\n", "    0.017875531688332558,\n", "    -0.0014096805825829506,\n", "    0.03829631954431534,\n", "    -0.021712996065616608,\n", "    -0.01208017859607935,\n", "    -0.010876077227294445,\n", "    -0.023749200627207756,\n", "    -0.051375024020671844,\n", "    -0.015320486389100552,\n", "    -0.023533832281827927,\n", "    -0.025667933747172356,\n", "    -0.009216764941811562,\n", "    0.038100533187389374,\n", "    -0.009334239177405834,\n", "    0.002905018627643585,\n", "    0.026372773572802544,\n", "    -0.025667933747172356,\n", "    -0.0011172207305207849,\n", "    -0.012089968658983707,\n", "    0.0023714937269687653,\n", "    0.03506580367684364,\n", "    -0.011336181312799454,\n", "    -0.011238286271691322,\n", "    -0.042212098836898804,\n", "    0.019853001460433006,\n", "    0.034791696816682816,\n", "    -0.08199641853570938,\n", "    0.04362178221344948,\n", "    0.024493198841810226,\n", "    0.006970087066292763,\n", "    0.021027734503149986,\n", "    -0.031580761075019836,\n", "    0.058580055832862854,\n", "    -0.04722429811954498,\n", "    0.02335762232542038,\n", "    0.03843337297439575,\n", "    -0.0464019849896431,\n", "    -0.03892284631729126,\n", "    -0.061399418860673904,\n", "    0.020616577938199043,\n", "    0.011737547814846039,\n", "    -0.00400143675506115,\n", "    -0.024747725576162338,\n", "    0.03821800649166107,\n", "    0.05341123044490814,\n", "    0.009784553200006485,\n", "    -0.0005420906818471849,\n", "    -0.04542303830385208,\n", "    -0.06265246868133545,\n", "    -0.030425604432821274,\n", "    0.0039940946735441685,\n", "    -0.02265278249979019,\n", "    -0.016759535297751427,\n", "    -0.028350241482257843,\n", "    -0.012305336073040962,\n", "    -0.038041796535253525,\n", "    -0.03737611323595047,\n", "    -0.024219093844294548,\n", "    -0.005281407386064529,\n", "    0.002789992606267333,\n", "    -0.0027850980404764414,\n", "    0.05176660045981407,\n", "    -0.015173644758760929,\n", "    -0.02175215445458889,\n", "    0.04084157571196556,\n", "    0.003886410966515541,\n", "    -0.02821318991482258,\n", "    -0.02932918630540371,\n", "    -0.002021521097049117,\n", "    0.011688601225614548,\n", "    -0.05157081410288811,\n", "    -0.04456156864762306,\n", "    0.03833547979593277,\n", "    -0.026842666789889336,\n", "    -0.01707279682159424,\n", "    0.01410659495741129,\n", "    -0.01829647831618786,\n", "    0.011189338751137257,\n", "    -0.0013962200609967113,\n", "    -0.008546188473701477,\n", "    -0.011708179488778114,\n", "    0.006456140894442797,\n", "    -0.01511490810662508,\n", "    -0.013940174132585526,\n", "    -0.0098922373726964,\n", "    -0.011257865466177464,\n", "    0.01010271068662405,\n", "    0.041663892567157745,\n", "    -0.038629163056612015,\n", "    -0.015389012172818184,\n", "    -0.04691103473305702,\n", "    0.03259886056184769,\n", "    0.048007454723119736,\n", "    0.015477117151021957,\n", "    -0.006906455848366022,\n", "    -0.006152668036520481,\n", "    -0.023905832320451736,\n", "    -0.005516353994607925,\n", "    -0.04781166464090347,\n", "    0.024395303800702095,\n", "    0.024219093844294548,\n", "    0.0032158337999135256,\n", "    -0.04287778213620186,\n", "    0.019804053008556366,\n", "    0.01362691167742014,\n", "    0.026216141879558563,\n", "    0.006994560826569796,\n", "    -0.017562270164489746,\n", "    0.0006546693621203303,\n", "    -0.014615645632147789,\n", "    -0.045188091695308685,\n", "    0.03768937662243843,\n", "    -0.01209975779056549,\n", "    0.06672488152980804,\n", "    -0.011669022031128407,\n", "    0.07001413404941559,\n", "    -0.050239447504282,\n", "    0.0346546471118927,\n", "    -0.01701406016945839,\n", "    -0.014273014850914478,\n", "    0.024042883887887,\n", "    0.02676435187458992,\n", "    0.03248138725757599,\n", "    -0.0015907853376120329,\n", "    -0.028291504830121994,\n", "    0.01551627553999424,\n", "    0.024082042276859283,\n", "    0.03269675746560097,\n", "    -0.003264780854806304,\n", "    0.005575090646743774,\n", "    0.02457151561975479,\n", "    -0.0068917712196707726,\n", "    -0.04761587455868721,\n", "    -0.022378677502274513,\n", "    -0.015868695452809334,\n", "    0.03947105631232262,\n", "    0.03694537654519081,\n", "    -0.03412601724267006,\n", "    0.009348923340439796,\n", "    -0.004370988346636295,\n", "    -0.016544166952371597,\n", "    0.026744771748781204,\n", "    -0.009162923321127892,\n", "    0.044444095343351364,\n", "    -0.014664593152701855,\n", "    -0.01610364206135273,\n", "    -0.008223135955631733,\n", "    -0.019549528136849403,\n", "    -0.010141868144273758,\n", "    0.027645401656627655,\n", "    0.0393340028822422,\n", "    -0.02324014902114868,\n", "    0.02047952450811863,\n", "    -0.010641129687428474,\n", "    -0.02431698888540268,\n", "    -0.04839903116226196,\n", "    0.016446271911263466,\n", "    0.059598159044981,\n", "    -0.010856498032808304,\n", "    0.023631727322936058,\n", "    -0.02057741954922676,\n", "    -0.010993550531566143,\n", "    0.005017091985791922,\n", "    0.057405322790145874,\n", "    -0.026588141918182373,\n", "    0.024591093882918358,\n", "    0.023044360801577568,\n", "    -0.016514798626303673,\n", "    -0.047380927950143814,\n", "    0.03224644064903259,\n", "    0.05854089930653572,\n", "    0.0005833899485878646,\n", "    -0.0032084917183965445,\n", "    -0.015291118063032627,\n", "    0.021340996026992798,\n", "    0.02490435540676117,\n", "    0.02484561875462532,\n", "    -0.018384583294391632,\n", "    0.04021505266427994,\n", "    -0.009745395742356777,\n", "    -0.06598088145256042,\n", "    0.00013299882994033396,\n", "    0.047537561506032944,\n", "    -0.03829631954431534,\n", "    0.030151499435305595,\n", "    -0.028722241520881653,\n", "    0.023964568972587585,\n", "    0.02809571661055088,\n", "    0.012011652812361717,\n", "    0.008115452714264393,\n", "    0.013499649241566658,\n", "    0.011708179488778114,\n", "    -0.011903968639671803,\n", "    -0.042525362223386765,\n", "    0.048438187688589096,\n", "    0.02169341780245304,\n", "    -0.033734437078237534,\n", "    -0.01582953706383705,\n", "    -0.008325926028192043,\n", "    0.005457617342472076,\n", "    0.019911738112568855,\n", "    -0.04557967185974121,\n", "    0.04142894595861435,\n", "    0.013411544263362885,\n", "    0.01743500679731369,\n", "    -0.02580498531460762,\n", "    -0.014302384108304977,\n", "    -0.0012151151895523071,\n", "    -0.07095392048358917,\n", "    -0.007116928696632385,\n", "    0.0260790903121233,\n", "    0.029622869566082954,\n", "    0.03684748336672783,\n", "    -0.020440367981791496,\n", "    0.03682790324091911,\n", "    -0.019167739897966385,\n", "    0.009255923330783844,\n", "    0.03721948340535164,\n", "    -0.005535932723432779,\n", "    0.01437090989202261,\n", "    0.04471819847822189,\n", "    -0.016123220324516296,\n", "    0.03447843715548515,\n", "    -0.0024436910171061754,\n", "    0.03696495667099953,\n", "    -0.026568561792373657,\n", "    -0.008580450899899006,\n", "    0.016172168776392937,\n", "    0.03257928416132927,\n", "    -0.05482091009616852,\n", "    0.01522259134799242,\n", "    -0.04663693159818649,\n", "    0.04781166464090347,\n", "    0.04510977864265442,\n", "    -0.015144276432693005,\n", "    0.008188873529434204,\n", "    0.06288741528987885,\n", "    -0.023631727322936058,\n", "    -0.0308759193867445,\n", "    -0.042995255440473557,\n", "    0.027899926528334618,\n", "    0.011199128814041615,\n", "    0.03608390688896179,\n", "    0.01215849444270134,\n", "    0.06136026233434677,\n", "    0.0004714231181424111,\n", "    0.008355294354259968,\n", "    -0.015075749717652798,\n", "    0.003157096914947033,\n", "    -0.015134486369788647,\n", "    0.07780653238296509,\n", "    -0.030993392691016197,\n", "    0.0048604607582092285,\n", "    -0.030366867780685425,\n", "    -0.02261362597346306,\n", "    0.03353865072131157,\n", "    0.03424349054694176,\n", "    -0.019265633076429367,\n", "    0.02549172192811966,\n", "    0.016133010387420654,\n", "    0.00866855587810278,\n", "    -0.03267717733979225,\n", "    0.011287233792245388,\n", "    0.014145752415060997,\n", "    0.01316680759191513,\n", "    -0.01162007451057434,\n", "    0.03492875024676323,\n", "    -0.023494675755500793,\n", "    -0.00930487085133791,\n", "    0.002008060459047556,\n", "    0.03365612402558327,\n", "    -0.10188857465982437,\n", "    -0.0023176518734544516,\n", "    0.012089968658983707,\n", "    0.01748395338654518,\n", "    -0.013196175917983055,\n", "    -0.03886410966515541,\n", "    -0.026216141879558563,\n", "    -0.03802221640944481,\n", "    0.005712143145501614,\n", "    0.02917255461215973,\n", "    -0.008541293442249298,\n", "    0.04037168249487877,\n", "    0.0231422558426857,\n", "    0.020675314590334892,\n", "    -0.0027606242801994085,\n", "    0.004782145377248526,\n", "    -0.028546029701828957,\n", "    0.05399859696626663,\n", "    0.04189883917570114,\n", "    -0.03181570768356323,\n", "    -0.006098825950175524,\n", "    0.005951984319835901,\n", "    -0.07283349335193634,\n", "    -0.004816408269107342,\n", "    0.002375164767727256,\n", "    -0.030582236126065254,\n", "    0.00943702831864357,\n", "    0.0009055239497683942,\n", "    0.00850213598459959,\n", "    0.013323439285159111,\n", "    -0.006426772568374872,\n", "    -0.03514412045478821,\n", "    0.019343949854373932,\n", "    -0.005594669375568628,\n", "    0.012001863680779934,\n", "    0.029309608042240143,\n", "    0.007229507435113192,\n", "    0.032128967344760895,\n", "    -0.016759535297751427,\n", "    0.03843337297439575,\n", "    -2.483689968357794e-05,\n", "    0.05262807384133339,\n", "    -0.03755232319235802,\n", "    -0.013597543351352215,\n", "    -0.06147773563861847,\n", "    0.01962784305214882,\n", "    0.03592727333307266,\n", "    0.021987101063132286,\n", "    0.023710044100880623,\n", "    0.036181800067424774,\n", "    0.04941713437438011,\n", "    -0.007513401564210653,\n", "    0.012197651900351048,\n", "    0.011522180400788784,\n", "    0.05348954349756241,\n", "    0.0017229428049176931,\n", "    0.006309299264103174,\n", "    0.012736071832478046,\n", "    0.0004916138714179397,\n", "    -0.004197225905954838,\n", "    -0.010327868163585663,\n", "    -0.02895718812942505,\n", "    -0.024336567148566246,\n", "    -0.012510914355516434,\n", "    0.02874181978404522,\n", "    0.04084157571196556,\n", "    0.017846163362264633,\n", "    -0.01769932173192501,\n", "    0.0010548130376264453,\n", "    0.010464919731020927,\n", "    0.030151499435305595,\n", "    0.015085539780557156,\n", "    0.04632366821169853,\n", "    0.0713455006480217,\n", "    -0.005457617342472076,\n", "    0.016916166990995407,\n", "    0.009255923330783844,\n", "    0.003964726347476244,\n", "    0.022848572582006454,\n", "    0.027175508439540863,\n", "    0.01754269003868103,\n", "    0.01641690358519554,\n", "    -0.005516353994607925,\n", "    -0.03205065429210663,\n", "    0.018169214949011803,\n", "    -0.07988189905881882,\n", "    -0.03279465064406395,\n", "    -0.019872579723596573,\n", "    0.01946142315864563,\n", "    0.01796363666653633,\n", "    0.002070468384772539,\n", "    0.0026578351389616728,\n", "    0.004517830442637205,\n", "    -0.00026875094044953585,\n", "    -0.006852613762021065,\n", "    -0.045031461864709854,\n", "    -0.0014084568247199059,\n", "    0.007087560370564461,\n", "    -0.01615258865058422,\n", "    -0.021458469331264496,\n", "    0.04847734794020653,\n", "    -0.007572138216346502,\n", "    0.016387535259127617,\n", "    0.017885321751236916,\n", "    0.021203944459557533,\n", "    -0.028820134699344635,\n", "    ...],\n", "   'details': 'defaultsession_cac6cec4',\n", "   'L_interaction': 11.0,\n", "   'R_recency': 1.0,\n", "   'N_visit': 27.0,\n", "   'H_segment': 39.0,\n", "   'timestamp': '2025-07-22 15:37:11',\n", "   'last_visit_time': '2025-07-22 18:49:43',\n", "   'access_count_lfu': 27.0,\n", "   'access_frequency': 27.0},\n", "  'distance': 41.850345611572266},\n", " {'output': {'id': 'session_7bd094bd',\n", "   'session_id': 'session_7bd094bd',\n", "   'summary': '要求被称呼为小可爱',\n", "   'summary_keywords': \"['小可爱', '称呼']\",\n", "   'summary_embedding': [-0.00014789807028137147,\n", "    -0.05977388471364975,\n", "    -0.008540496230125427,\n", "    -0.032858382910490036,\n", "    -0.017473988234996796,\n", "    -0.015863660722970963,\n", "    0.06418312340974808,\n", "    0.06663695722818375,\n", "    -0.0029906113632023335,\n", "    0.07595386356115341,\n", "    0.015518588945269585,\n", "    -0.04263538122177124,\n", "    0.03205322101712227,\n", "    -0.008401509374380112,\n", "    -0.05524962767958641,\n", "    -0.04646949842572212,\n", "    0.05985056608915329,\n", "    0.04520424082875252,\n", "    0.09638970345258713,\n", "    -0.0025377064011991024,\n", "    -0.05785682797431946,\n", "    0.02139437384903431,\n", "    -0.06836231052875519,\n", "    -0.02768232487142086,\n", "    -0.030347036197781563,\n", "    0.023924890905618668,\n", "    -0.013984941877424717,\n", "    0.0973098948597908,\n", "    0.0652950182557106,\n", "    0.00631670793518424,\n", "    -0.0928623154759407,\n", "    -0.0387437529861927,\n", "    -0.028391636908054352,\n", "    -0.015144762583076954,\n", "    0.015087250620126724,\n", "    -0.012096639722585678,\n", "    -0.005257532931864262,\n", "    -0.04692959412932396,\n", "    -0.04681456834077835,\n", "    0.051990628242492676,\n", "    0.005027486011385918,\n", "    -0.04957513511180878,\n", "    0.03128639608621597,\n", "    -0.019122660160064697,\n", "    0.02591863088309765,\n", "    -0.024231620132923126,\n", "    0.0004942416562698781,\n", "    -0.004361308179795742,\n", "    0.02900509536266327,\n", "    -0.012604660354554653,\n", "    -0.007538832724094391,\n", "    -0.023752355948090553,\n", "    0.0019362290622666478,\n", "    -0.0019649851601570845,\n", "    0.016074536368250847,\n", "    -0.09447264671325684,\n", "    0.008914322592318058,\n", "    0.03237912058830261,\n", "    0.01787657104432583,\n", "    -0.0249025896191597,\n", "    -0.09125198423862457,\n", "    0.006757631432265043,\n", "    0.06514165550470352,\n", "    0.07595386356115341,\n", "    0.00011442443064879626,\n", "    0.010879307053983212,\n", "    -0.006211269646883011,\n", "    -0.008516533300280571,\n", "    -0.04409234598278999,\n", "    -0.0112435482442379,\n", "    -0.017905326560139656,\n", "    -0.04896167665719986,\n", "    -0.052489060908555984,\n", "    0.021643592044711113,\n", "    0.03306926041841507,\n", "    -0.009642804972827435,\n", "    -0.01849961467087269,\n", "    -0.031209712848067284,\n", "    0.030155330896377563,\n", "    0.008703446015715599,\n", "    0.005842235870659351,\n", "    0.025995314121246338,\n", "    -0.0004142643592786044,\n", "    -0.041446805000305176,\n", "    -0.022544609382748604,\n", "    0.056629907339811325,\n", "    0.08442725986242294,\n", "    0.027183890342712402,\n", "    0.022985532879829407,\n", "    0.027107207104563713,\n", "    -0.010591748170554638,\n", "    0.08404384553432465,\n", "    0.009355246089398861,\n", "    -0.04094837233424187,\n", "    0.07921285927295685,\n", "    -0.011751568876206875,\n", "    -0.02545853704214096,\n", "    -0.01188576314598322,\n", "    -0.01046714000403881,\n", "    0.0020848012063652277,\n", "    0.0013611115282401443,\n", "    -0.008784920908510685,\n", "    0.004727945663034916,\n", "    0.04439907521009445,\n", "    -0.011483180336654186,\n", "    0.033912766724824905,\n", "    -0.12414871156215668,\n", "    0.017579426988959312,\n", "    -0.011607789434492588,\n", "    -0.014876374043524265,\n", "    0.032187413424253464,\n", "    0.013170192018151283,\n", "    0.05820189788937569,\n", "    -0.012968900613486767,\n", "    0.02542019635438919,\n", "    0.03393193706870079,\n", "    0.0381111241877079,\n", "    -0.0066378153860569,\n", "    -0.0002322935761185363,\n", "    -0.017368551343679428,\n", "    0.02045501582324505,\n", "    -0.035158853977918625,\n", "    -0.0004514073661994189,\n", "    -0.020397502928972244,\n", "    0.027739837765693665,\n", "    0.02003326267004013,\n", "    -0.041523490101099014,\n", "    5.1034196076216176e-05,\n", "    0.028870901092886925,\n", "    -0.013333141803741455,\n", "    0.025746095925569534,\n", "    -0.04685291275382042,\n", "    -0.035542264580726624,\n", "    -0.02971440739929676,\n", "    -0.0072368960827589035,\n", "    -0.0022273825015872717,\n", "    0.004529051017016172,\n", "    -0.009561330080032349,\n", "    0.03659664839506149,\n", "    0.001751712174154818,\n", "    -0.02998279593884945,\n", "    -0.014109550975263119,\n", "    0.0001354671549052,\n", "    -0.031248053535819054,\n", "    -0.010428798384964466,\n", "    0.009273771196603775,\n", "    -0.02854500152170658,\n", "    -0.008660311810672283,\n", "    0.007289614994078875,\n", "    0.01384116243571043,\n", "    0.02264046110212803,\n", "    0.024845078587532043,\n", "    -0.021701103076338768,\n", "    0.014607985503971577,\n", "    -0.01730145327746868,\n", "    0.009149162098765373,\n", "    -0.012192492373287678,\n", "    -0.007543625310063362,\n", "    0.0247108843177557,\n", "    -0.035542264580726624,\n", "    -0.02331143245100975,\n", "    0.003407571464776993,\n", "    -0.022544609382748604,\n", "    -0.03860956057906151,\n", "    -0.026685453951358795,\n", "    -0.03755517676472664,\n", "    -0.04259704053401947,\n", "    0.009671560488641262,\n", "    -0.013093509711325169,\n", "    -0.025937801226973534,\n", "    -0.03604070097208023,\n", "    0.039184678345918655,\n", "    0.018729662522673607,\n", "    0.050687026232481,\n", "    -0.003795775817707181,\n", "    0.002121944213286042,\n", "    -0.03807278349995613,\n", "    -0.03364437818527222,\n", "    -0.03619406744837761,\n", "    0.0016918041510507464,\n", "    -0.008152292110025883,\n", "    0.013850747607648373,\n", "    -0.015997854992747307,\n", "    0.01290180440992117,\n", "    0.005818272475153208,\n", "    0.002513743005692959,\n", "    0.037497665733098984,\n", "    -0.041331782937049866,\n", "    0.007936622947454453,\n", "    -0.006436523981392384,\n", "    -0.024768397212028503,\n", "    0.01436835341155529,\n", "    -0.009863266721367836,\n", "    0.012662171386182308,\n", "    -0.011895348317921162,\n", "    -0.01161737460643053,\n", "    0.034219495952129364,\n", "    -0.04324883967638016,\n", "    0.012892218306660652,\n", "    -0.010457554832100868,\n", "    -0.027854859828948975,\n", "    0.02854500152170658,\n", "    0.027107207104563713,\n", "    0.014147891663014889,\n", "    -0.01905556209385395,\n", "    -0.01395618636161089,\n", "    0.007572381291538477,\n", "    -0.008943078108131886,\n", "    -0.01003580167889595,\n", "    -0.015834903344511986,\n", "    0.023119725286960602,\n", "    -0.000413665286032483,\n", "    0.05153053253889084,\n", "    0.003481857478618622,\n", "    0.007002056110650301,\n", "    -0.035043831914663315,\n", "    -0.026187019422650337,\n", "    0.03243663161993027,\n", "    0.001476135104894638,\n", "    -0.006129794754087925,\n", "    0.016505874693393707,\n", "    0.0029954039491713047,\n", "    0.018068276345729828,\n", "    0.004869328811764717,\n", "    -0.030001966282725334,\n", "    -0.04405400529503822,\n", "    0.04106339439749718,\n", "    -0.014838033355772495,\n", "    -0.012815535999834538,\n", "    0.004859743639826775,\n", "    0.009072479791939259,\n", "    0.006283159367740154,\n", "    -0.02557356096804142,\n", "    0.028870901092886925,\n", "    0.05701332166790962,\n", "    0.0015384395373985171,\n", "    0.009844095446169376,\n", "    -0.009968704544007778,\n", "    0.054137732833623886,\n", "    0.0033907971810549498,\n", "    0.01995657943189144,\n", "    -0.00514250947162509,\n", "    0.012345856986939907,\n", "    -0.008324827067553997,\n", "    -0.010409628041088581,\n", "    0.01479969173669815,\n", "    0.01764652505517006,\n", "    0.03983647748827934,\n", "    -0.006230440456420183,\n", "    -0.0810532346367836,\n", "    -0.0039731040596961975,\n", "    -0.05459782853722572,\n", "    -0.03581065312027931,\n", "    0.03306926041841507,\n", "    0.00941275805234909,\n", "    -0.021720273420214653,\n", "    0.015374809503555298,\n", "    -0.014655912294983864,\n", "    -0.002851624507457018,\n", "    -0.008066023699939251,\n", "    -0.016582557931542397,\n", "    -0.0044475761242210865,\n", "    -0.01577739231288433,\n", "    -0.005425275769084692,\n", "    0.10037718713283539,\n", "    0.003989878110587597,\n", "    -0.019113074988126755,\n", "    -0.0005259929457679391,\n", "    -0.020953450351953506,\n", "    -0.0038724581245332956,\n", "    -0.032628338783979416,\n", "    -0.0011873781913891435,\n", "    0.02448083832859993,\n", "    -0.003714300924912095,\n", "    0.014962641522288322,\n", "    -0.03295423835515976,\n", "    -0.0243274737149477,\n", "    0.0013024016516283154,\n", "    0.022659631446003914,\n", "    0.07001098245382309,\n", "    -0.0534859336912632,\n", "    -0.028525831177830696,\n", "    -0.0020129114855080843,\n", "    -0.06153757870197296,\n", "    0.0022525438107550144,\n", "    0.02018662728369236,\n", "    0.017512330785393715,\n", "    0.06349298357963562,\n", "    -0.028199931606650352,\n", "    0.03178483247756958,\n", "    -0.009115613996982574,\n", "    -0.026838818565011024,\n", "    0.01182825118303299,\n", "    0.025190148502588272,\n", "    0.012499221600592136,\n", "    0.0029570627957582474,\n", "    0.04957513511180878,\n", "    0.05770346149802208,\n", "    -0.045549310743808746,\n", "    0.019937409088015556,\n", "    0.014885959215462208,\n", "    -0.039683111011981964,\n", "    -0.030423719435930252,\n", "    -0.012652586214244366,\n", "    0.012230833061039448,\n", "    -0.005981222726404667,\n", "    0.056744933128356934,\n", "    0.01220207754522562,\n", "    -0.012489636428654194,\n", "    0.048003144562244415,\n", "    0.10881224274635315,\n", "    0.009216259233653545,\n", "    0.0297910887748003,\n", "    -0.058815356343984604,\n", "    -0.04114007577300072,\n", "    0.0035513509064912796,\n", "    0.011962445452809334,\n", "    0.06284118443727493,\n", "    -0.04853992164134979,\n", "    -0.014109550975263119,\n", "    -0.006867862306535244,\n", "    0.010054972022771835,\n", "    -0.00155161926522851,\n", "    -0.03690337762236595,\n", "    0.007898281328380108,\n", "    -0.022985532879829407,\n", "    0.0003354852378834039,\n", "    0.013678212650120258,\n", "    0.04857826232910156,\n", "    -0.03690337762236595,\n", "    0.046124428510665894,\n", "    0.010975160636007786,\n", "    -0.018212055787444115,\n", "    -0.012441709637641907,\n", "    0.019649850204586983,\n", "    0.028985925018787384,\n", "    0.05367763713002205,\n", "    0.016563385725021362,\n", "    0.04524258151650429,\n", "    -0.009599670767784119,\n", "    0.0013431392144411802,\n", "    0.045549310743808746,\n", "    -0.08419721573591232,\n", "    -0.00023843414965085685,\n", "    0.0006949337548576295,\n", "    -0.07549376785755157,\n", "    0.03699922934174538,\n", "    0.05785682797431946,\n", "    -0.012326686643064022,\n", "    -0.011281889863312244,\n", "    -0.012537563219666481,\n", "    0.034219495952129364,\n", "    0.032935068011283875,\n", "    0.03212990239262581,\n", "    -0.034775443375110626,\n", "    -0.018336664885282516,\n", "    -0.04248201847076416,\n", "    0.05597810819745064,\n", "    0.006038734223693609,\n", "    -0.05130048468708992,\n", "    0.009249807335436344,\n", "    -0.09056184440851212,\n", "    -0.01267175655812025,\n", "    -0.03235995024442673,\n", "    -0.02436581440269947,\n", "    -0.005823065526783466,\n", "    0.058777015656232834,\n", "    0.005027486011385918,\n", "    0.0198798980563879,\n", "    -0.026608772575855255,\n", "    -0.03717176616191864,\n", "    0.008789713494479656,\n", "    0.01683177426457405,\n", "    -0.02643623761832714,\n", "    -0.021375203505158424,\n", "    0.024039914831519127,\n", "    0.046047747135162354,\n", "    0.011224377900362015,\n", "    0.0056457375176250935,\n", "    -0.011387327685952187,\n", "    -0.03673084080219269,\n", "    0.013534433208405972,\n", "    -0.037075914442539215,\n", "    -0.009872851893305779,\n", "    0.0320148803293705,\n", "    0.016956383362412453,\n", "    0.028564171865582466,\n", "    -0.01741647720336914,\n", "    -0.02662794291973114,\n", "    -0.0004984352272003889,\n", "    0.03189985454082489,\n", "    0.03644328564405441,\n", "    -0.023924890905618668,\n", "    -0.031842343509197235,\n", "    0.000978898024186492,\n", "    -0.015125591307878494,\n", "    -0.08496403694152832,\n", "    -0.019544411450624466,\n", "    -0.03017450124025345,\n", "    -0.020991791039705276,\n", "    0.014492962509393692,\n", "    -0.035983189940452576,\n", "    -0.0029067399445921183,\n", "    -0.0054348609410226345,\n", "    0.017387721687555313,\n", "    -0.004416423849761486,\n", "    0.006249610800296068,\n", "    0.06966590881347656,\n", "    0.034104473888874054,\n", "    0.010160410776734352,\n", "    0.045587651431560516,\n", "    -0.01854754239320755,\n", "    0.01321811880916357,\n", "    -0.03061542473733425,\n", "    0.04110173508524895,\n", "    -0.005990807898342609,\n", "    0.1313568502664566,\n", "    0.01686052978038788,\n", "    0.032896727323532104,\n", "    0.00566970044746995,\n", "    0.006407767999917269,\n", "    0.04685291275382042,\n", "    -0.011483180336654186,\n", "    0.01120520755648613,\n", "    0.0022465530782938004,\n", "    -0.01340982411056757,\n", "    -0.0677105113863945,\n", "    -0.016036195680499077,\n", "    -0.0029043436516076326,\n", "    0.012930559925734997,\n", "    -0.01229793019592762,\n", "    -0.019669020548462868,\n", "    0.04915338009595871,\n", "    -0.03721010684967041,\n", "    -0.05977388471364975,\n", "    0.006441316567361355,\n", "    0.04313381761312485,\n", "    -0.035312220454216,\n", "    0.021490227431058884,\n", "    -0.03571480140089989,\n", "    0.05306417867541313,\n", "    0.06046402454376221,\n", "    -0.017502745613455772,\n", "    -0.03573397174477577,\n", "    -0.039606429636478424,\n", "    0.039299700409173965,\n", "    0.008880773559212685,\n", "    0.012556733563542366,\n", "    -0.011166865937411785,\n", "    -0.005013108253479004,\n", "    -0.010294604115188122,\n", "    -0.006700119469314814,\n", "    -0.020205797627568245,\n", "    0.08013305068016052,\n", "    -0.03216824308037758,\n", "    -0.06541004031896591,\n", "    -0.02459586039185524,\n", "    0.05383100360631943,\n", "    0.010476725175976753,\n", "    0.02218036726117134,\n", "    -0.018748832866549492,\n", "    0.0019266437739133835,\n", "    0.0039515369571745396,\n", "    -0.022774655371904373,\n", "    0.029963623732328415,\n", "    -0.0072560664266347885,\n", "    0.010400042869150639,\n", "    -0.04021988809108734,\n", "    -0.029503529891371727,\n", "    -0.01706182211637497,\n", "    -0.009110821411013603,\n", "    0.021106814965605736,\n", "    0.04508921876549721,\n", "    0.0020069207530468702,\n", "    -0.02384820766746998,\n", "    0.029465189203619957,\n", "    -0.03166980668902397,\n", "    -0.03565729036927223,\n", "    -0.00403061555698514,\n", "    -0.014617571607232094,\n", "    -0.04635447636246681,\n", "    0.00387006183154881,\n", "    -0.046239450573921204,\n", "    -0.0534859336912632,\n", "    0.030327865853905678,\n", "    -0.04685291275382042,\n", "    0.01827915385365486,\n", "    0.01924726739525795,\n", "    -0.015566514804959297,\n", "    0.010495895519852638,\n", "    0.043670594692230225,\n", "    0.0028827767819166183,\n", "    -0.023330602794885635,\n", "    -0.04666120558977127,\n", "    -0.008842432871460915,\n", "    -0.017167259007692337,\n", "    -0.03247497230768204,\n", "    -0.060732413083314896,\n", "    -0.015077665448188782,\n", "    -0.03084547072649002,\n", "    0.046124428510665894,\n", "    0.02252543717622757,\n", "    0.013563188724219799,\n", "    0.07944291085004807,\n", "    0.03732512891292572,\n", "    -0.016294999048113823,\n", "    -0.02670462615787983,\n", "    -0.006934959441423416,\n", "    0.018183300271630287,\n", "    -0.020780915394425392,\n", "    0.05770346149802208,\n", "    -0.017905326560139656,\n", "    -0.00336683401837945,\n", "    0.001471342402510345,\n", "    -0.0003989878168795258,\n", "    -0.04712129756808281,\n", "    -0.04301879554986954,\n", "    0.028391636908054352,\n", "    0.0050562419928610325,\n", "    0.030116990208625793,\n", "    -0.033989448100328445,\n", "    0.04190690070390701,\n", "    0.052067309617996216,\n", "    -0.014176648110151291,\n", "    0.06452818959951401,\n", "    0.058853697031736374,\n", "    -0.02956104278564453,\n", "    -0.02440415509045124,\n", "    0.05041864141821861,\n", "    0.03377857431769371,\n", "    -0.007064360659569502,\n", "    0.030385376885533333,\n", "    -0.03351018577814102,\n", "    0.014780521392822266,\n", "    0.007337541319429874,\n", "    -0.012326686643064022,\n", "    0.010313775390386581,\n", "    0.04631613567471504,\n", "    -0.0006691732560284436,\n", "    0.02049335651099682,\n", "    0.02553522028028965,\n", "    -0.030979664996266365,\n", "    -0.011991200968623161,\n", "    -0.06207435578107834,\n", "    -0.019266439601778984,\n", "    -0.012403368949890137,\n", "    0.02093428000807762,\n", "    0.012000786140561104,\n", "    0.056668251752853394,\n", "    0.04282708838582039,\n", "    0.03529305011034012,\n", "    0.01596909761428833,\n", "    0.013850747607648373,\n", "    -0.009082064963877201,\n", "    -0.030308695510029793,\n", "    0.002662315033376217,\n", "    0.029541872441768646,\n", "    -0.017550671473145485,\n", "    -0.04643115773797035,\n", "    0.02967606671154499,\n", "    -0.04275040701031685,\n", "    -0.03611738234758377,\n", "    -0.007658648770302534,\n", "    0.026187019422650337,\n", "    -0.010879307053983212,\n", "    -0.01952524110674858,\n", "    0.031880684196949005,\n", "    -0.0019553997553884983,\n", "    0.033126771450042725,\n", "    0.025094296783208847,\n", "    0.01909390278160572,\n", "    -0.06667529791593552,\n", "    -0.0016606519930064678,\n", "    0.019822385162115097,\n", "    -0.005880577024072409,\n", "    -0.10306106507778168,\n", "    -0.011157280765473843,\n", "    0.015921171754598618,\n", "    -0.060732413083314896,\n", "    -0.04455244168639183,\n", "    -0.006479657720774412,\n", "    -0.0316314660012722,\n", "    -0.022659631446003914,\n", "    -0.02463420294225216,\n", "    -0.023177238181233406,\n", "    -0.0060051861219108105,\n", "    0.007610722444951534,\n", "    -0.013361898250877857,\n", "    -0.05509626120328903,\n", "    -0.030270354822278023,\n", "    -0.005128131713718176,\n", "    -0.04758139327168465,\n", "    -0.011780324392020702,\n", "    0.005938088987022638,\n", "    -0.0022549401037395,\n", "    0.005233570002019405,\n", "    0.013151021674275398,\n", "    -0.026877161115407944,\n", "    0.04930674657225609,\n", "    -0.010955989360809326,\n", "    0.0043732901103794575,\n", "    -0.0004615917569026351,\n", "    -0.014080794528126717,\n", "    -0.0021039717830717564,\n", "    -0.01640043593943119,\n", "    0.0076011368073523045,\n", "    -0.04957513511180878,\n", "    -0.004409234970808029,\n", "    0.019045976921916008,\n", "    0.09247890114784241,\n", "    -0.009987874887883663,\n", "    -0.013179777190089226,\n", "    -0.07319329679012299,\n", "    -0.005104168318212032,\n", "    -0.026685453951358795,\n", "    -0.0367116704583168,\n", "    -0.04923006519675255,\n", "    -0.06180596724152565,\n", "    0.014080794528126717,\n", "    -0.04424571245908737,\n", "    -0.00882326252758503,\n", "    -0.01024667825549841,\n", "    -0.003937159199267626,\n", "    0.03374022990465164,\n", "    -0.008545288816094398,\n", "    0.011952860280871391,\n", "    -0.02764398418366909,\n", "    0.003330889157950878,\n", "    -0.011042256839573383,\n", "    0.01062050461769104,\n", "    -0.025937801226973534,\n", "    0.034890465438365936,\n", "    -0.007179384119808674,\n", "    0.0024873835500329733,\n", "    0.0014318031026050448,\n", "    0.01077386923134327,\n", "    -0.01632375456392765,\n", "    -0.004037804901599884,\n", "    0.02041667327284813,\n", "    -0.01999492011964321,\n", "    -0.017282282933592796,\n", "    -0.00802289042621851,\n", "    -0.008713031187653542,\n", "    0.01826956868171692,\n", "    -0.00647007254883647,\n", "    0.016620898619294167,\n", "    -0.023522308096289635,\n", "    0.010198751464486122,\n", "    0.04639281705021858,\n", "    -0.002145907375961542,\n", "    0.023598991334438324,\n", "    0.015000983141362667,\n", "    0.025055954232811928,\n", "    0.024576690047979355,\n", "    -0.024384984746575356,\n", "    0.05076371133327484,\n", "    -0.018451688811182976,\n", "    -0.017358966171741486,\n", "    0.05356261506676674,\n", "    -0.024921761825680733,\n", "    -0.009283356368541718,\n", "    0.024998443201184273,\n", "    0.006163343321532011,\n", "    0.02319640852510929,\n", "    -0.0295993834733963,\n", "    -0.029100948944687843,\n", "    0.0680939182639122,\n", "    0.03178483247756958,\n", "    -0.02264046110212803,\n", "    0.008521325886249542,\n", "    -0.03212990239262581,\n", "    -0.04704461619257927,\n", "    -0.02545853704214096,\n", "    0.028391636908054352,\n", "    -0.03826449066400528,\n", "    0.013064754195511341,\n", "    -0.04516590014100075,\n", "    -0.016017025336623192,\n", "    -0.015393979847431183,\n", "    -0.06640691310167313,\n", "    -0.01991823874413967,\n", "    -0.00774012366309762,\n", "    0.007083531469106674,\n", "    -0.03862873092293739,\n", "    0.030001966282725334,\n", "    0.004042597487568855,\n", "    -0.01374530978500843,\n", "    0.025324342772364616,\n", "    0.010802624747157097,\n", "    -0.04742802679538727,\n", "    -0.01824081316590309,\n", "    -0.014397109858691692,\n", "    -0.003565728897228837,\n", "    -0.008320034481585026,\n", "    0.013697382993996143,\n", "    -0.006033941637724638,\n", "    0.006225647404789925,\n", "    -0.02869836613535881,\n", "    -0.02783568948507309,\n", "    -0.015470662154257298,\n", "    -0.039529748260974884,\n", "    0.007188969291746616,\n", "    -0.02486424893140793,\n", "    -0.0022549401037395,\n", "    0.010793039575219154,\n", "    0.004234303254634142,\n", "    -0.07338500022888184,\n", "    -0.005013108253479004,\n", "    -0.0019709758926182985,\n", "    0.011972030624747276,\n", "    0.02421244978904724,\n", "    0.02147105522453785,\n", "    -0.03237912058830261,\n", "    0.0009321697289124131,\n", "    0.006680949125438929,\n", "    0.02986777201294899,\n", "    0.03452622517943382,\n", "    0.03653913736343384,\n", "    -0.019726533442735672,\n", "    -0.04428405314683914,\n", "    0.03166980668902397,\n", "    -0.02538185566663742,\n", "    0.011406498029828072,\n", "    0.010007046163082123,\n", "    0.022717144340276718,\n", "    0.02003326267004013,\n", "    0.019113074988126755,\n", "    0.015844490379095078,\n", "    -0.002042865613475442,\n", "    -0.0326475091278553,\n", "    -0.0010286218021064997,\n", "    -0.06188264861702919,\n", "    -0.015863660722970963,\n", "    -0.031094688922166824,\n", "    0.03134390711784363,\n", "    -0.0029954039491713047,\n", "    0.027893202379345894,\n", "    0.011358572170138359,\n", "    0.04006652534008026,\n", "    -0.014770936220884323,\n", "    0.06748045980930328,\n", "    -0.024538349360227585,\n", "    -0.04275040701031685,\n", "    0.021911978721618652,\n", "    -0.017128918319940567,\n", "    0.0067336680367589,\n", "    -0.04397732391953468,\n", "    -0.015336467884480953,\n", "    0.014627156779170036,\n", "    -0.0023891343735158443,\n", "    -0.014675082638859749,\n", "    -0.05367763713002205,\n", "    0.01103267166763544,\n", "    -0.0005748180556111038,\n", "    -0.009657182730734348,\n", "    -0.0034962354693561792,\n", "    -0.0538693442940712,\n", "    -0.005688871257007122,\n", "    0.04079500585794449,\n", "    0.0668286606669426,\n", "    -0.008171462453901768,\n", "    0.03897380083799362,\n", "    0.032110732048749924,\n", "    0.014972226694226265,\n", "    0.04190690070390701,\n", "    -0.0022537419572472572,\n", "    0.026532089337706566,\n", "    -0.03561894968152046,\n", "    0.07530206441879272,\n", "    0.014885959215462208,\n", "    0.006407767999917269,\n", "    -0.008794506080448627,\n", "    0.020320821553468704,\n", "    0.03740181401371956,\n", "    -0.002837246749550104,\n", "    -0.054291099309921265,\n", "    0.060962460935115814,\n", "    -0.009863266721367836,\n", "    -0.045932721346616745,\n", "    0.007438187021762133,\n", "    -0.011233963072299957,\n", "    0.007903073914349079,\n", "    0.04083334654569626,\n", "    -0.06498828530311584,\n", "    -0.03165063634514809,\n", "    -0.015116006135940552,\n", "    -0.020474186167120934,\n", "    0.0008063627756200731,\n", "    -0.005271911155432463,\n", "    0.01013165432959795,\n", "    -0.0042031509801745415,\n", "    -0.02670462615787983,\n", "    -0.01952524110674858,\n", "    -0.004931633360683918,\n", "    0.012384198606014252,\n", "    0.0013994527980685234,\n", "    -0.01698513887822628,\n", "    0.0017061821417883039,\n", "    0.0202633086591959,\n", "    0.026071995496749878,\n", "    -0.009216259233653545,\n", "    0.02436581440269947,\n", "    -0.007951000705361366,\n", "    0.012374612502753735,\n", "    0.026570431888103485,\n", "    0.0014234159607440233,\n", "    -0.01501056831330061,\n", "    0.02908177860081196,\n", "    0.02639789693057537,\n", "    0.014492962509393692,\n", "    0.031688980758190155,\n", "    0.026417067274451256,\n", "    -0.0006113619892857969,\n", "    0.024078255519270897,\n", "    -0.01686052978038788,\n", "    0.014253330416977406,\n", "    -0.0267429668456316,\n", "    0.018451688811182976,\n", "    0.022199537605047226,\n", "    0.002873191609978676,\n", "    -0.0032781700138002634,\n", "    0.02482590824365616,\n", "    0.004514672793447971,\n", "    -0.003721489803865552,\n", "    0.050111908465623856,\n", "    0.005818272475153208,\n", "    0.034219495952129364,\n", "    -0.004612922202795744,\n", "    -0.052067309617996216,\n", "    -0.017234357073903084,\n", "    0.006853484082967043,\n", "    -0.07192803919315338,\n", "    0.02147105522453785,\n", "    0.04393898323178291,\n", "    0.08020973205566406,\n", "    0.02158607915043831,\n", "    -0.008382339030504227,\n", "    -0.015719881281256676,\n", "    0.02568858489394188,\n", "    -0.013917844742536545,\n", "    -0.014675082638859749,\n", "    0.03460290655493736,\n", "    0.000686546612996608,\n", "    -0.001516872551292181,\n", "    -0.02787403017282486,\n", "    0.04681456834077835,\n", "    0.004572184756398201,\n", "    0.024979272857308388,\n", "    -0.019707363098859787,\n", "    0.014885959215462208,\n", "    0.020512526854872704,\n", "    0.05045698210597038,\n", "    -0.06767217069864273,\n", "    -0.034142814576625824,\n", "    -0.003970707766711712,\n", "    0.025209320709109306,\n", "    0.03979813680052757,\n", "    -0.007610722444951534,\n", "    0.004768683109432459,\n", "    0.05099375545978546,\n", "    -0.004414027556777,\n", "    -0.04290376976132393,\n", "    -0.06690534204244614,\n", "    -0.02588029019534588,\n", "    -0.010888892225921154,\n", "    0.04393898323178291,\n", "    0.014234159141778946,\n", "    0.01636209525167942,\n", "    -0.0017193618696182966,\n", "    0.012623830698430538,\n", "    0.009096442721784115,\n", "    -0.029771918430924416,\n", "    -0.015211859717965126,\n", "    0.021835297346115112,\n", "    -0.03416198492050171,\n", "    0.06498828530311584,\n", "    -0.008205010555684566,\n", "    -0.03698005899786949,\n", "    -0.005875784438103437,\n", "    0.08074650913476944,\n", "    -0.017982009798288345,\n", "    0.02635955438017845,\n", "    0.013342726975679398,\n", "    -0.030634595081210136,\n", "    -0.010428798384964466,\n", "    -0.008890359662473202,\n", "    -0.034027788788080215,\n", "    -0.008952663280069828,\n", "    -0.06686700135469437,\n", "    0.0006176523165777326,\n", "    -0.0036831486504524946,\n", "    0.00594288157299161,\n", "    -0.010371286422014236,\n", "    -0.004411631263792515,\n", "    -0.07714243978261948,\n", "    0.0032877554185688496,\n", "    -0.005880577024072409,\n", "    0.019151415675878525,\n", "    0.0032757737208157778,\n", "    -0.030596254393458366,\n", "    0.028449147939682007,\n", "    -0.025075126439332962,\n", "    0.005247947759926319,\n", "    -0.03140142187476158,\n", "    0.016266241669654846,\n", "    0.029848601669073105,\n", "    0.03592567890882492,\n", "    -0.0005376750486902893,\n", "    -0.01936229132115841,\n", "    0.005674493499100208,\n", "    0.010553407482802868,\n", "    0.01894053816795349,\n", "    0.022506266832351685,\n", "    -0.01476135104894638,\n", "    -0.01647711917757988,\n", "    0.019266439601778984,\n", "    -0.018566712737083435,\n", "    -0.025401026010513306,\n", "    0.043670594692230225,\n", "    -0.012307516299188137,\n", "    0.04508921876549721,\n", "    0.0070451898500323296,\n", "    -0.0326475091278553,\n", "    -0.02206534333527088,\n", "    0.046162769198417664,\n", "    0.02022496797144413,\n", "    -0.03682669624686241,\n", "    0.028353296220302582,\n", "    0.007117079570889473,\n", "    0.02733725495636463,\n", "    0.021413544192910194,\n", "    -0.01476135104894638,\n", "    0.03306926041841507,\n", "    -0.023368943482637405,\n", "    0.0064988285303115845,\n", "    -0.001997335348278284,\n", "    -0.0498051792383194,\n", "    0.00562177412211895,\n", "    0.020761745050549507,\n", "    0.0354464128613472,\n", "    -0.012815535999834538,\n", "    -0.03579148277640343,\n", "    0.02584194950759411,\n", "    0.014282085932791233,\n", "    0.03122888319194317,\n", "    0.004025822971016169,\n", "    0.006009978707879782,\n", "    0.00864593405276537,\n", "    0.05471285060048103,\n", "    0.012393783777952194,\n", "    -0.00966676790267229,\n", "    -0.006661778315901756,\n", "    -0.06134587153792381,\n", "    -0.009403171949088573,\n", "    0.01241295412182808,\n", "    -0.031171372160315514,\n", "    0.00012251202133484185,\n", "    0.028909241780638695,\n", "    0.001941021764650941,\n", "    0.021796956658363342,\n", "    0.04639281705021858,\n", "    -0.002729412168264389,\n", "    -0.0019422199111431837,\n", "    0.020685061812400818,\n", "    0.07564713060855865,\n", "    0.037152595818042755,\n", "    0.03763186186552048,\n", "    0.020014092326164246,\n", "    0.015240615233778954,\n", "    0.014751764945685863,\n", "    -0.0014665498165413737,\n", "    0.025516049936413765,\n", "    -0.013112680055201054,\n", "    0.04485917091369629,\n", "    0.023752355948090553,\n", "    -0.004195962101221085,\n", "    -0.005132924299687147,\n", "    0.006863069720566273,\n", "    0.0028084907680749893,\n", "    -0.01182825118303299,\n", "    -0.058661989867687225,\n", "    -0.0012203275691717863,\n", "    0.04876996949315071,\n", "    0.01901722140610218,\n", "    -0.014291671104729176,\n", "    -0.009206674061715603,\n", "    -0.0022106082178652287,\n", "    0.048233192414045334,\n", "    0.0052671185694634914,\n", "    0.012250004336237907,\n", "    0.02143271453678608,\n", "    0.04209860786795616,\n", "    0.022122856229543686,\n", "    0.015566514804959297,\n", "    -0.016486704349517822,\n", "    -0.0028875693678855896,\n", "    0.017675280570983887,\n", "    0.06909079104661942,\n", "    -0.0018020350253209472,\n", "    -0.049958545714616776,\n", "    ...],\n", "   'details': 'defaultsession_7bd094bd',\n", "   'L_interaction': 1.0,\n", "   'R_recency': 1.0,\n", "   'N_visit': 29.0,\n", "   'H_segment': 31.0,\n", "   'timestamp': '2025-07-22 11:00:53',\n", "   'last_visit_time': '2025-07-22 18:38:24',\n", "   'access_count_lfu': 29.0,\n", "   'access_frequency': 29.0},\n", "  'distance': 41.887046813964844},\n", " {'output': {'id': 'session_6f26bf60',\n", "   'session_id': 'session_6f26bf60',\n", "   'summary': '用户表达挽留情绪',\n", "   'summary_keywords': \"['回来', '哀求']\",\n", "   'summary_embedding': [-0.004758098628371954,\n", "    -0.04942033439874649,\n", "    -0.010634234175086021,\n", "    -0.027286291122436523,\n", "    0.02582305297255516,\n", "    -0.08894836157560349,\n", "    0.053129952400922775,\n", "    -0.003683855291455984,\n", "    -0.03425212576985359,\n", "    0.08239470422267914,\n", "    -0.03926010802388191,\n", "    0.026276450604200363,\n", "    -0.0250399112701416,\n", "    -0.010778496973216534,\n", "    -0.06508316099643707,\n", "    0.028728919103741646,\n", "    -0.04764796420931816,\n", "    -0.0076047140173614025,\n", "    0.08631040900945663,\n", "    -0.00860424991697073,\n", "    -0.016528403386473656,\n", "    -0.06079649180173874,\n", "    -0.06656701117753983,\n", "    -0.040352385491132736,\n", "    0.03406664356589317,\n", "    0.026523757725954056,\n", "    -0.027863342314958572,\n", "    0.07814925163984299,\n", "    -0.010031421668827534,\n", "    0.07571739703416824,\n", "    -0.04103248193860054,\n", "    -0.04385590925812721,\n", "    -0.07246117293834686,\n", "    -0.014003802090883255,\n", "    -0.02600853331387043,\n", "    -0.013952280394732952,\n", "    0.003279404016211629,\n", "    -0.06805085390806198,\n", "    -0.012241734191775322,\n", "    0.04303155094385147,\n", "    0.027368726208806038,\n", "    -0.011324634775519371,\n", "    0.043320078402757645,\n", "    -0.0019282277207821608,\n", "    0.007481059990823269,\n", "    -0.05420161783695221,\n", "    -0.023370584473013878,\n", "    0.013818321749567986,\n", "    0.04125918075442314,\n", "    0.002735842252150178,\n", "    -0.031325649470090866,\n", "    0.012025340460240841,\n", "    0.0071822297759354115,\n", "    0.020134974271059036,\n", "    -0.003537016222253442,\n", "    -0.05226437374949455,\n", "    -0.022505007684230804,\n", "    0.03425212576985359,\n", "    -0.015147601254284382,\n", "    -0.00839815940707922,\n", "    -0.07089489698410034,\n", "    0.02623523212969303,\n", "    -0.027368726208806038,\n", "    0.01071667019277811,\n", "    0.019351832568645477,\n", "    -0.0234324112534523,\n", "    0.01831107959151268,\n", "    0.03363385796546936,\n", "    -0.06735014915466309,\n", "    -0.008238440379500389,\n", "    0.0001796845463104546,\n", "    -0.017888594418764114,\n", "    -0.011448289267718792,\n", "    0.020093755796551704,\n", "    0.005816884804517031,\n", "    -0.025287220254540443,\n", "    -0.010309642180800438,\n", "    0.04426809027791023,\n", "    0.0024550447706133127,\n", "    0.0045082145370543,\n", "    -0.009382238611578941,\n", "    0.11145336925983429,\n", "    0.04946155473589897,\n", "    -0.006337261758744717,\n", "    0.004693695344030857,\n", "    0.04068212956190109,\n", "    0.010392078198492527,\n", "    -0.0033489593770354986,\n", "    0.030253984034061432,\n", "    0.035076484084129333,\n", "    0.06512437760829926,\n", "    0.05197585001587868,\n", "    -0.014900293201208115,\n", "    -0.021969173103570938,\n", "    0.03738469257950783,\n", "    -0.009536805562675,\n", "    0.0234324112534523,\n", "    -0.006053888238966465,\n", "    -0.012664218433201313,\n", "    -0.001599772134795785,\n", "    -0.02109329216182232,\n", "    -0.0073522538878023624,\n", "    -0.06821572780609131,\n", "    -0.04107369855046272,\n", "    -0.03060433641076088,\n", "    0.0716780349612236,\n", "    -0.12010914087295532,\n", "    0.008897927589714527,\n", "    0.021330295130610466,\n", "    -0.011252503842115402,\n", "    -0.0032304576598107815,\n", "    -0.007893240079283714,\n", "    0.0675562396645546,\n", "    -0.024174334481358528,\n", "    0.04187744855880737,\n", "    0.00232108635827899,\n", "    0.03357202932238579,\n", "    0.0001353269472019747,\n", "    -0.0010033997241407633,\n", "    -0.015559780411422253,\n", "    0.017661895602941513,\n", "    0.02070172131061554,\n", "    -0.016549011692404747,\n", "    0.0073419492691755295,\n", "    -0.017053931951522827,\n", "    0.02242257073521614,\n", "    -0.04896693676710129,\n", "    0.009361629374325275,\n", "    0.04818379878997803,\n", "    -0.03919828310608864,\n", "    0.03342776745557785,\n", "    -0.07163681834936142,\n", "    0.008655771613121033,\n", "    0.02324693091213703,\n", "    0.00025407009525224566,\n", "    0.009768656454980373,\n", "    -0.00564686069265008,\n", "    -0.00829511508345604,\n", "    0.01789890043437481,\n", "    -0.036313023418188095,\n", "    -0.034293342381715775,\n", "    -0.007115250453352928,\n", "    0.007589257322251797,\n", "    -0.05729296803474426,\n", "    0.015157905407249928,\n", "    -0.004044512286782265,\n", "    0.017950423061847687,\n", "    0.007805651519447565,\n", "    0.013818321749567986,\n", "    -0.04076456278562546,\n", "    0.009449217468500137,\n", "    0.007099793758243322,\n", "    -0.019454877823591232,\n", "    -0.017929812893271446,\n", "    0.012592087499797344,\n", "    -0.0020892354659736156,\n", "    -0.01683753728866577,\n", "    0.012540564872324467,\n", "    0.04542219266295433,\n", "    0.002807973651215434,\n", "    -0.0007696166285313666,\n", "    0.00926888920366764,\n", "    -0.0041346768848598,\n", "    0.003611723892390728,\n", "    -0.018300775438547134,\n", "    -0.024916257709264755,\n", "    0.0010001795599237084,\n", "    -0.0194136593490839,\n", "    -0.012653914280235767,\n", "    -0.028955617919564247,\n", "    -0.004369103815406561,\n", "    0.020938724279403687,\n", "    0.02178369276225567,\n", "    0.002771907951682806,\n", "    -0.0056520127691328526,\n", "    -0.030542509630322456,\n", "    -0.020165888592600822,\n", "    -2.8820371881010942e-05,\n", "    -0.020681113004684448,\n", "    -0.0023197983391582966,\n", "    0.01640474796295166,\n", "    -0.019465181976556778,\n", "    -0.0049822209402918816,\n", "    0.0021046921610832214,\n", "    0.030253984034061432,\n", "    -0.03540622815489769,\n", "    0.03987837955355644,\n", "    -0.0013949703425168991,\n", "    0.008897927589714527,\n", "    -0.043938346207141876,\n", "    -0.06236277520656586,\n", "    -0.011437984183430672,\n", "    0.024524686858057976,\n", "    0.0027487229090183973,\n", "    -0.016239875927567482,\n", "    -0.025926098227500916,\n", "    0.004691119305789471,\n", "    -0.018970567733049393,\n", "    -0.006723680067807436,\n", "    -0.039692897349596024,\n", "    -0.031923312693834305,\n", "    0.04459783434867859,\n", "    0.05848828703165054,\n", "    0.034499432891607285,\n", "    -0.02324693091213703,\n", "    -0.00977380946278572,\n", "    0.022340135648846626,\n", "    -0.0035730821546167135,\n", "    -0.029738759621977806,\n", "    -0.008557879365980625,\n", "    -0.026358885690569878,\n", "    0.03443760797381401,\n", "    -0.002720385557040572,\n", "    0.005868407431989908,\n", "    -0.009160691872239113,\n", "    0.008300267159938812,\n", "    -0.0059250821359455585,\n", "    0.03919828310608864,\n", "    0.01271574106067419,\n", "    -0.016136832535266876,\n", "    0.03616876155138016,\n", "    -0.007810803595930338,\n", "    0.012241734191775322,\n", "    -0.018187426030635834,\n", "    -0.01771341823041439,\n", "    -0.049131810665130615,\n", "    0.022319525480270386,\n", "    -0.02380337193608284,\n", "    0.027368726208806038,\n", "    0.007826260291039944,\n", "    -0.04468027129769325,\n", "    0.007754128891974688,\n", "    0.027842732146382332,\n", "    0.0215982124209404,\n", "    0.025946706533432007,\n", "    0.002467925427481532,\n", "    0.03499405086040497,\n", "    -0.08515630662441254,\n", "    0.05449014529585838,\n", "    -0.003931162878870964,\n", "    -0.025081129744648933,\n", "    -0.03662215918302536,\n", "    0.044103220105171204,\n", "    0.006996748968958855,\n", "    -0.012818786315619946,\n", "    0.03829148784279823,\n", "    0.0038384227082133293,\n", "    -0.003343807067722082,\n", "    -0.032953761518001556,\n", "    -0.04921424761414528,\n", "    -0.02982119470834732,\n", "    -0.04228962957859039,\n", "    0.00545107526704669,\n", "    -0.007965371012687683,\n", "    0.040929436683654785,\n", "    -0.04097065329551697,\n", "    -0.01060332078486681,\n", "    0.03480856865644455,\n", "    0.005188310984522104,\n", "    0.020547153428196907,\n", "    0.013035180047154427,\n", "    0.0001164085406344384,\n", "    -0.025060521438717842,\n", "    0.00840846449136734,\n", "    0.12035644799470901,\n", "    -0.017929812893271446,\n", "    -0.029470842331647873,\n", "    0.01851717010140419,\n", "    -0.046699948608875275,\n", "    -0.028852572664618492,\n", "    -0.027080200612545013,\n", "    0.026317669078707695,\n", "    -0.0013885301304981112,\n", "    0.015518562868237495,\n", "    0.033736903220415115,\n", "    0.004181046970188618,\n", "    0.012674523517489433,\n", "    -0.009253432042896748,\n", "    -0.0006678598001599312,\n", "    0.024730777367949486,\n", "    -0.09018489718437195,\n", "    0.005538663361221552,\n", "    0.0010104840621352196,\n", "    -0.016002872958779335,\n", "    0.04946155473589897,\n", "    0.016641752794384956,\n", "    -0.005221800412982702,\n", "    0.046535078436136246,\n", "    -0.0062548257410526276,\n", "    0.005384096410125494,\n", "    0.04016690328717232,\n", "    0.03798235207796097,\n", "    -0.04245449975132942,\n", "    -0.023659110069274902,\n", "    -0.03186148405075073,\n", "    -0.0028826810885220766,\n", "    -0.02258744277060032,\n", "    0.07612957805395126,\n", "    0.004057392943650484,\n", "    -0.027492379769682884,\n", "    0.0027152332477271557,\n", "    -0.006641244050115347,\n", "    -0.007687150035053492,\n", "    -0.017940117046236992,\n", "    0.016827233135700226,\n", "    -3.0692084692418575e-05,\n", "    0.025369655340909958,\n", "    -0.00829511508345604,\n", "    0.014869379810988903,\n", "    0.021515775471925735,\n", "    0.10658964514732361,\n", "    -0.0030166395008563995,\n", "    0.01799163967370987,\n", "    -0.045133668929338455,\n", "    -0.02122724987566471,\n", "    -0.00035550491884350777,\n", "    -0.02600853331387043,\n", "    0.05119270831346512,\n", "    -0.015343385748565197,\n", "    -0.002181975869461894,\n", "    0.01171620562672615,\n", "    0.0028311586938798428,\n", "    -0.030253984034061432,\n", "    0.017888594418764114,\n", "    -0.01851717010140419,\n", "    -0.01222112588584423,\n", "    0.05523206666111946,\n", "    -0.004096034914255142,\n", "    0.03544744849205017,\n", "    0.016961190849542618,\n", "    0.03656033053994179,\n", "    -0.06549534201622009,\n", "    -0.010500275529921055,\n", "    0.028131259605288506,\n", "    0.022566834464669228,\n", "    0.029264751821756363,\n", "    0.04587559029459953,\n", "    0.051151491701602936,\n", "    -0.004425778519362211,\n", "    -0.013756494969129562,\n", "    -0.006043583620339632,\n", "    -0.027471771463751793,\n", "    0.002905866364017129,\n", "    -0.02563757263123989,\n", "    -0.014869379810988903,\n", "    -0.010459057986736298,\n", "    0.0019861904438585043,\n", "    0.06285738945007324,\n", "    -0.011200981214642525,\n", "    -0.010201445780694485,\n", "    0.044927578419446945,\n", "    0.015240341424942017,\n", "    -0.007785042282193899,\n", "    -0.016847841441631317,\n", "    -0.04237206280231476,\n", "    -0.01360192708671093,\n", "    -0.012653914280235767,\n", "    0.04202171042561531,\n", "    -0.0420423224568367,\n", "    0.019609445706009865,\n", "    -0.010273576714098454,\n", "    -0.10395170003175735,\n", "    0.01856869086623192,\n", "    0.02740994468331337,\n", "    -0.033098023384809494,\n", "    -0.01589982956647873,\n", "    0.007573800627142191,\n", "    0.009552262723445892,\n", "    -0.0001227683387696743,\n", "    0.024957476183772087,\n", "    -0.032479751855134964,\n", "    -0.005770514719188213,\n", "    -0.0006987732485868037,\n", "    0.030686771497130394,\n", "    0.039940204471349716,\n", "    -0.013560709543526173,\n", "    -0.0021162847988307476,\n", "    -0.03406664356589317,\n", "    0.005780819337815046,\n", "    0.0572105310857296,\n", "    0.030253984034061432,\n", "    0.015889523550868034,\n", "    -0.0035473208408802748,\n", "    0.050533220171928406,\n", "    0.04777161777019501,\n", "    -0.004611259326338768,\n", "    -0.04101186990737915,\n", "    0.02938840724527836,\n", "    -0.014704507775604725,\n", "    0.00909886509180069,\n", "    -0.0023919297382235527,\n", "    -0.030130330473184586,\n", "    0.04208353906869888,\n", "    0.010953673161566257,\n", "    -0.011252503842115402,\n", "    0.013818321749567986,\n", "    -0.14113029837608337,\n", "    -0.06223912164568901,\n", "    0.005554120521992445,\n", "    -0.01510638277977705,\n", "    0.04160953313112259,\n", "    0.056509826332330704,\n", "    0.004621563944965601,\n", "    0.004685967229306698,\n", "    -0.04018751159310341,\n", "    -0.02304084040224552,\n", "    0.01782676763832569,\n", "    0.00801689364016056,\n", "    -0.013941975310444832,\n", "    -0.003774019656702876,\n", "    0.09587297588586807,\n", "    0.007187381852418184,\n", "    -0.03019215725362301,\n", "    -0.020598676055669785,\n", "    -0.017012713477015495,\n", "    -0.002684319857507944,\n", "    0.07946822792291641,\n", "    0.008588792756199837,\n", "    0.06326957046985626,\n", "    0.03522074967622757,\n", "    -0.004047088790684938,\n", "    0.049131810665130615,\n", "    -0.032170619815588,\n", "    -0.0840846449136734,\n", "    -0.00860424991697073,\n", "    -0.013447360135614872,\n", "    0.03184087574481964,\n", "    -0.013787408359348774,\n", "    -0.02543148212134838,\n", "    0.016487184911966324,\n", "    -0.026915328577160835,\n", "    -0.027760297060012817,\n", "    0.004402593709528446,\n", "    0.025452090427279472,\n", "    -0.0907619521021843,\n", "    0.0038487270940095186,\n", "    0.05502597987651825,\n", "    0.008975211530923843,\n", "    0.007939609698951244,\n", "    0.005116179585456848,\n", "    -0.02357667312026024,\n", "    0.03785869851708412,\n", "    -0.025349047034978867,\n", "    -0.05086296424269676,\n", "    0.0019861904438585043,\n", "    0.017012713477015495,\n", "    -0.019537312909960747,\n", "    -0.028151867911219597,\n", "    -0.020897505804896355,\n", "    0.05098661780357361,\n", "    -0.008114785887300968,\n", "    -0.02299962192773819,\n", "    0.018578996881842613,\n", "    0.001805861946195364,\n", "    0.02221648208796978,\n", "    -0.02924414351582527,\n", "    0.020134974271059036,\n", "    -0.004915242083370686,\n", "    0.028048822656273842,\n", "    0.019660968333482742,\n", "    -0.036951903253793716,\n", "    -0.03419030085206032,\n", "    -0.009330715984106064,\n", "    -0.009907767176628113,\n", "    0.005291355773806572,\n", "    0.04315520450472832,\n", "    -0.023349974304437637,\n", "    -0.03443760797381401,\n", "    -0.0047065760008990765,\n", "    0.002934203715994954,\n", "    0.014014107175171375,\n", "    0.01109793595969677,\n", "    0.035921454429626465,\n", "    -0.030047893524169922,\n", "    -0.007027662359178066,\n", "    -0.026173405349254608,\n", "    -0.003567929845303297,\n", "    -0.04966764524579048,\n", "    -0.026729848235845566,\n", "    0.061538416892290115,\n", "    0.018486255779862404,\n", "    0.07551130652427673,\n", "    0.00269720028154552,\n", "    -0.014261414296925068,\n", "    0.055726684629917145,\n", "    -0.046699948608875275,\n", "    -0.021969173103570938,\n", "    0.05366578698158264,\n", "    -0.009093713015317917,\n", "    0.019867056980729103,\n", "    0.0066824620589613914,\n", "    0.013787408359348774,\n", "    0.016528403386473656,\n", "    -0.019444573670625687,\n", "    0.01971249096095562,\n", "    -0.009851092472672462,\n", "    0.004840534180402756,\n", "    -0.027368726208806038,\n", "    -0.0337781198322773,\n", "    -0.019331224262714386,\n", "    -0.0374465174973011,\n", "    -0.0017813887679949403,\n", "    0.004969340283423662,\n", "    0.05840585008263588,\n", "    -0.05247046425938606,\n", "    -0.045133668929338455,\n", "    -0.032088182866573334,\n", "    0.012664218433201313,\n", "    -0.017105454578995705,\n", "    0.030934080481529236,\n", "    -0.006244521122425795,\n", "    -0.03787930682301521,\n", "    0.011149458587169647,\n", "    0.030295202508568764,\n", "    0.006950378883630037,\n", "    -0.014127456583082676,\n", "    -0.03602449968457222,\n", "    0.058323416858911514,\n", "    -0.009866549633443356,\n", "    -0.025307828560471535,\n", "    0.013622536323964596,\n", "    0.05857072398066521,\n", "    -0.022896578535437584,\n", "    -0.018991176038980484,\n", "    -0.021866127848625183,\n", "    0.14055325090885162,\n", "    0.002147198189049959,\n", "    -0.020784156396985054,\n", "    0.026173405349254608,\n", "    0.03441699966788292,\n", "    0.025163564831018448,\n", "    -0.0028749527409672737,\n", "    0.02759542502462864,\n", "    0.023700328543782234,\n", "    0.001077463268302381,\n", "    0.01582769677042961,\n", "    0.0030630098190158606,\n", "    0.01858930103480816,\n", "    -0.016270790249109268,\n", "    -0.02961510606110096,\n", "    0.038312096148729324,\n", "    0.02318510413169861,\n", "    0.009037038311362267,\n", "    -0.040908828377723694,\n", "    0.02780151553452015,\n", "    -0.023762155324220657,\n", "    -0.002655982505530119,\n", "    0.03478796035051346,\n", "    0.044556617736816406,\n", "    -0.008393007330596447,\n", "    0.016734492033720016,\n", "    -0.050904180854558945,\n", "    0.011396766640245914,\n", "    -0.01883660815656185,\n", "    -0.008310571312904358,\n", "    -0.009887158870697021,\n", "    0.022298917174339294,\n", "    0.012045949697494507,\n", "    -0.004374256357550621,\n", "    0.03713738173246384,\n", "    -0.029470842331647873,\n", "    0.021309686824679375,\n", "    0.018970567733049393,\n", "    -0.008738207630813122,\n", "    0.042949117720127106,\n", "    0.00040638333302922547,\n", "    0.03754956275224686,\n", "    0.0415889248251915,\n", "    0.023473629727959633,\n", "    0.0018702649977058172,\n", "    0.01178833656013012,\n", "    -0.013395837508141994,\n", "    -0.02219587191939354,\n", "    0.045339759439229965,\n", "    0.05477867275476456,\n", "    -0.04323764145374298,\n", "    -0.03041885606944561,\n", "    -0.00036516538239084184,\n", "    -0.026709239929914474,\n", "    -0.03396360203623772,\n", "    -0.0014258838491514325,\n", "    0.015528867021203041,\n", "    -0.0019552770536392927,\n", "    0.021144814789295197,\n", "    -0.02520478330552578,\n", "    0.010830019600689411,\n", "    0.05185219645500183,\n", "    0.008253896608948708,\n", "    -0.011932600289583206,\n", "    -0.06223912164568901,\n", "    0.015673130750656128,\n", "    -0.04059969261288643,\n", "    0.007527430076152086,\n", "    0.02743055298924446,\n", "    0.04125918075442314,\n", "    -0.016363531351089478,\n", "    0.013921366073191166,\n", "    -0.0036426372826099396,\n", "    0.04057908430695534,\n", "    0.024071289226412773,\n", "    0.016260486096143723,\n", "    0.02219587191939354,\n", "    -0.015085773542523384,\n", "    -0.048719629645347595,\n", "    -0.008815491572022438,\n", "    0.02341180294752121,\n", "    -0.032912541180849075,\n", "    -0.015467040240764618,\n", "    0.0032021203078329563,\n", "    0.04082639142870903,\n", "    -0.008413616567850113,\n", "    -0.0026791675481945276,\n", "    -0.04649386182427406,\n", "    0.015054860152304173,\n", "    0.016734492033720016,\n", "    -0.008166308514773846,\n", "    -0.059930916875600815,\n", "    -0.0010748871136456728,\n", "    -0.055149633437395096,\n", "    -0.03326289355754852,\n", "    -0.004770978819578886,\n", "    -0.045133668929338455,\n", "    -0.010376621969044209,\n", "    0.009104017168283463,\n", "    0.005201191641390324,\n", "    0.04637020826339722,\n", "    0.01863051950931549,\n", "    0.006548503413796425,\n", "    0.0018084381008520722,\n", "    -0.044515397399663925,\n", "    -0.06425879895687103,\n", "    0.0040367841720581055,\n", "    -0.009933528490364552,\n", "    -0.005765362177044153,\n", "    0.05242924764752388,\n", "    -0.00489463284611702,\n", "    -0.028955617919564247,\n", "    -0.034932222217321396,\n", "    -0.023679718375205994,\n", "    -0.03165539354085922,\n", "    0.014560244977474213,\n", "    -0.01260239165276289,\n", "    -0.018352298066020012,\n", "    0.01840382069349289,\n", "    0.01210777647793293,\n", "    0.037899915128946304,\n", "    -0.01833168789744377,\n", "    0.0024962627794593573,\n", "    0.04501001536846161,\n", "    -0.07138951122760773,\n", "    0.028172476217150688,\n", "    0.04946155473589897,\n", "    -0.00796021893620491,\n", "    0.010500275529921055,\n", "    -0.02122724987566471,\n", "    0.024875039234757423,\n", "    -0.020021624863147736,\n", "    0.02223709039390087,\n", "    0.01961974985897541,\n", "    -0.009438913315534592,\n", "    -0.02681228332221508,\n", "    -0.06108501926064491,\n", "    0.030501291155815125,\n", "    0.007408928591758013,\n", "    0.002281156601384282,\n", "    -0.07802560180425644,\n", "    0.04665873199701309,\n", "    0.043897129595279694,\n", "    0.005744753405451775,\n", "    -0.030707381665706635,\n", "    -0.015096078626811504,\n", "    -0.04443296417593956,\n", "    0.013972888700664043,\n", "    0.033510204404592514,\n", "    -0.031717222183942795,\n", "    -0.037116773426532745,\n", "    -0.02899683639407158,\n", "    -0.021742474287748337,\n", "    -0.07171925157308578,\n", "    -0.048760849982500076,\n", "    -0.004606107249855995,\n", "    -0.027533598244190216,\n", "    -0.009954137727618217,\n", "    -0.0028929857071489096,\n", "    0.044350527226924896,\n", "    -5.458159648696892e-05,\n", "    -0.009304954670369625,\n", "    0.008944297209382057,\n", "    0.046741168946027756,\n", "    -0.024565905332565308,\n", "    -0.007934457622468472,\n", "    -0.021289076656103134,\n", "    -0.020258627831935883,\n", "    -0.027265680953860283,\n", "    0.018434733152389526,\n", "    0.040950044989585876,\n", "    -0.02942962385714054,\n", "    -0.019444573670625687,\n", "    -0.00684733409434557,\n", "    -0.040311165153980255,\n", "    -0.0015224884264171124,\n", "    0.016765406355261803,\n", "    0.016827233135700226,\n", "    -0.010840323753654957,\n", "    -0.04554584622383118,\n", "    -0.03728164732456207,\n", "    1.4842894415778574e-06,\n", "    -0.015621607191860676,\n", "    -0.05341847985982895,\n", "    -0.02141273021697998,\n", "    0.041527096182107925,\n", "    0.0033283503726124763,\n", "    0.007934457622468472,\n", "    -0.008583640679717064,\n", "    0.03103712573647499,\n", "    0.0314699150621891,\n", "    0.03404603525996208,\n", "    -0.03600388765335083,\n", "    -0.04818379878997803,\n", "    -0.03662215918302536,\n", "    0.00382554205134511,\n", "    -0.028955617919564247,\n", "    0.026935938745737076,\n", "    0.03926010802388191,\n", "    -0.008794882334768772,\n", "    -0.044103220105171204,\n", "    0.030954688787460327,\n", "    -0.0057550580240786076,\n", "    0.01732184924185276,\n", "    -0.029553279280662537,\n", "    -0.015374300070106983,\n", "    0.02118603140115738,\n", "    0.01999071054160595,\n", "    0.00945436954498291,\n", "    0.040311165153980255,\n", "    -0.025142956525087357,\n", "    0.06714405864477158,\n", "    0.03460247814655304,\n", "    0.049626424908638,\n", "    -0.054943542927503586,\n", "    0.044721487909555435,\n", "    -0.020279238000512123,\n", "    -0.024813212454319,\n", "    -0.010386926122009754,\n", "    0.0009332004119642079,\n", "    0.02918231673538685,\n", "    0.010654843412339687,\n", "    -0.04735943675041199,\n", "    0.03227366507053375,\n", "    0.0056520127691328526,\n", "    0.02380337193608284,\n", "    -0.030975298956036568,\n", "    -0.00570353539660573,\n", "    0.02049563080072403,\n", "    -0.02959449589252472,\n", "    -0.028481611981987953,\n", "    0.011149458587169647,\n", "    -0.026379495859146118,\n", "    0.03408725559711456,\n", "    0.023370584473013878,\n", "    -0.012963049113750458,\n", "    0.0006227776175364852,\n", "    -0.02031015045940876,\n", "    0.01734245754778385,\n", "    0.027904560789465904,\n", "    -0.039486806839704514,\n", "    0.0016075004823505878,\n", "    -0.048925720155239105,\n", "    0.02763664349913597,\n", "    0.015889523550868034,\n", "    -0.012087167240679264,\n", "    -0.010289033874869347,\n", "    -0.012334475293755531,\n", "    0.018321383744478226,\n", "    -0.02281414158642292,\n", "    -0.026771066710352898,\n", "    0.0029651171062141657,\n", "    -0.0011012924369424582,\n", "    -0.03346898406744003,\n", "    0.0024550447706133127,\n", "    0.027657251805067062,\n", "    -0.014148064889013767,\n", "    0.024071289226412773,\n", "    -0.032912541180849075,\n", "    -0.01773402839899063,\n", "    -0.006419697310775518,\n", "    0.05003860592842102,\n", "    -0.00745529867708683,\n", "    0.014240805990993977,\n", "    0.008619706146419048,\n", "    0.011005195789039135,\n", "    0.0006884687463752925,\n", "    0.022278308868408203,\n", "    0.04123856872320175,\n", "    -0.04443296417593956,\n", "    0.005404705181717873,\n", "    -0.014282023534178734,\n", "    0.014756030403077602,\n", "    0.002250242978334427,\n", "    0.0035756581928581,\n", "    0.007769585587084293,\n", "    0.036148153245449066,\n", "    -0.009237975813448429,\n", "    -0.06751502305269241,\n", "    0.0064351544715464115,\n", "    -0.03806478902697563,\n", "    -0.022257698699831963,\n", "    0.007924153469502926,\n", "    0.017857681959867477,\n", "    0.0005007338477298617,\n", "    -0.011623465456068516,\n", "    0.0053222691640257835,\n", "    0.017126062884926796,\n", "    0.05960117280483246,\n", "    -0.006352718453854322,\n", "    0.005080113653093576,\n", "    -0.06207425147294998,\n", "    0.04657629504799843,\n", "    0.034499432891607285,\n", "    -0.03144930303096771,\n", "    -0.010474514216184616,\n", "    -0.02139212191104889,\n", "    0.006033279001712799,\n", "    -0.002180687850341201,\n", "    0.017960727214813232,\n", "    0.06553655862808228,\n", "    0.02040289156138897,\n", "    0.014828161336481571,\n", "    -0.027286291122436523,\n", "    -0.01573495753109455,\n", "    0.02524600178003311,\n", "    -0.041712578386068344,\n", "    0.008990667760372162,\n", "    0.02083567902445793,\n", "    0.04286668077111244,\n", "    0.02761603333055973,\n", "    -0.016229571774601936,\n", "    -0.032170619815588,\n", "    -0.027059592306613922,\n", "    -0.007924153469502926,\n", "    0.04123856872320175,\n", "    -0.017837073653936386,\n", "    0.02780151553452015,\n", "    0.00029673712560907006,\n", "    -0.039136454463005066,\n", "    0.014199587516486645,\n", "    -0.05778758227825165,\n", "    0.0448039248585701,\n", "    -0.014065629802644253,\n", "    0.0011508828029036522,\n", "    0.016167744994163513,\n", "    0.041321005672216415,\n", "    -0.045710720121860504,\n", "    -0.014240805990993977,\n", "    -0.009057646617293358,\n", "    0.03936315327882767,\n", "    0.052511684596538544,\n", "    0.0002244446804979816,\n", "    0.01199442707002163,\n", "    0.03779686987400055,\n", "    -0.025534527376294136,\n", "    -0.048348668962717056,\n", "    -0.04360860213637352,\n", "    -0.017971031367778778,\n", "    -0.02302023209631443,\n", "    0.0005480700638145208,\n", "    0.012334475293755531,\n", "    0.04315520450472832,\n", "    0.028955617919564247,\n", "    0.009088560938835144,\n", "    -0.00974289607256651,\n", "    0.019145743921399117,\n", "    -0.0007019934128038585,\n", "    0.05585033819079399,\n", "    -0.03802356868982315,\n", "    0.004904937464743853,\n", "    -0.015425821766257286,\n", "    -0.04909059405326843,\n", "    0.013004266656935215,\n", "    0.042701806873083115,\n", "    -0.03536501154303551,\n", "    0.02324693091213703,\n", "    0.0032021203078329563,\n", "    -0.031676001846790314,\n", "    -0.05947751924395561,\n", "    0.028564047068357468,\n", "    0.025349047034978867,\n", "    -0.030521901324391365,\n", "    -0.014189283363521099,\n", "    -0.026379495859146118,\n", "    -0.035509273409843445,\n", "    -0.02497808448970318,\n", "    0.021721865981817245,\n", "    0.026523757725954056,\n", "    -0.06619604676961899,\n", "    0.03713738173246384,\n", "    0.02477199397981167,\n", "    0.026544367894530296,\n", "    -0.007810803595930338,\n", "    -0.017795855179429054,\n", "    -0.016147136688232422,\n", "    -0.06862790882587433,\n", "    -0.007074032444506884,\n", "    -0.002174247521907091,\n", "    0.01461176760494709,\n", "    -0.00539440056309104,\n", "    0.0034545804373919964,\n", "    -0.0010600744280964136,\n", "    -0.0288319643586874,\n", "    0.02143334038555622,\n", "    0.014354155398905277,\n", "    0.017332153394818306,\n", "    0.08239470422267914,\n", "    -0.04323764145374298,\n", "    -0.026915328577160835,\n", "    -0.03742590919137001,\n", "    -0.038126613944768906,\n", "    -0.015271254815161228,\n", "    0.007244056556373835,\n", "    -0.011252503842115402,\n", "    0.023700328543782234,\n", "    0.036539722234010696,\n", "    0.03280949592590332,\n", "    0.034334562718868256,\n", "    0.013004266656935215,\n", "    -0.00935132522135973,\n", "    0.021969173103570938,\n", "    -0.0047890120185911655,\n", "    0.010082944296300411,\n", "    0.07217264920473099,\n", "    0.03458186984062195,\n", "    0.019423963502049446,\n", "    -0.0007007053354755044,\n", "    0.008501204662024975,\n", "    0.006048735696822405,\n", "    -0.006481524556875229,\n", "    0.0010265848832204938,\n", "    0.013890452682971954,\n", "    -0.04315520450472832,\n", "    0.0376732163131237,\n", "    0.043691039085388184,\n", "    0.008264201693236828,\n", "    0.01980523020029068,\n", "    0.031923312693834305,\n", "    0.05247046425938606,\n", "    -0.002869800664484501,\n", "    0.03256218880414963,\n", "    0.03645728528499603,\n", "    0.03680764138698578,\n", "    0.008228135295212269,\n", "    -0.03604510799050331,\n", "    0.01399349793791771,\n", "    -0.01299396250396967,\n", "    -0.015889523550868034,\n", "    0.00023539320682175457,\n", "    -0.02819308638572693,\n", "    -0.019702184945344925,\n", "    -0.015467040240764618,\n", "    -0.027657251805067062,\n", "    0.003936315421015024,\n", "    -0.006017822306603193,\n", "    -0.039940204471349716,\n", "    0.034128472208976746,\n", "    0.00639908853918314,\n", "    0.006079649552702904,\n", "    -0.009119474329054356,\n", "    0.024483468383550644,\n", "    0.02499869279563427,\n", "    0.010134465992450714,\n", "    0.003771443385630846,\n", "    -0.008696990087628365,\n", "    0.011685292236506939,\n", "    0.01140707079321146,\n", "    0.025452090427279472,\n", "    0.017012713477015495,\n", "    0.014539635740220547,\n", "    -0.025843661278486252,\n", "    0.003980109468102455,\n", "    -0.015199122950434685,\n", "    -0.05164610594511032,\n", "    -0.08321906626224518,\n", "    -0.029058663174510002,\n", "    0.023329365998506546,\n", "    0.011561638675630093,\n", "    0.03967228904366493,\n", "    -0.040950044989585876,\n", "    -0.012623000890016556,\n", "    -0.022917186841368675,\n", "    0.017455806955695152,\n", "    -0.03645728528499603,\n", "    0.0024820941034704447,\n", "    0.039940204471349716,\n", "    -0.031676001846790314,\n", "    -0.0005306812818162143,\n", "    0.003513831179589033,\n", "    0.04966764524579048,\n", "    0.04156831279397011,\n", "    0.012066558003425598,\n", "    -0.001179864164441824,\n", "    -0.03435517102479935,\n", "    ...],\n", "   'details': 'defaultsession_6f26bf60',\n", "   'L_interaction': 2.0,\n", "   'R_recency': 1.0,\n", "   'N_visit': 0.0,\n", "   'H_segment': 3.0,\n", "   'timestamp': '2025-07-22 18:49:34',\n", "   'last_visit_time': '2025-07-22 18:49:34',\n", "   'access_count_lfu': 0.0,\n", "   'access_frequency': 0.0},\n", "  'distance': 42.04181671142578}]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["search_similar_sessions(query_embedding=[0.2]*1024)"]}, {"cell_type": "code", "execution_count": 63, "id": "8b9a002e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-21 17:23:20,527 - INFO - ✅ Delete collection 'mid_term_record_page' with schema.\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["drop_collection(collection_name=\"mid_term_record_page\")"]}, {"cell_type": "code", "execution_count": 22, "id": "19db7401", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-22 19:10:31,035 - INFO - ✅ Created collection 'mid_term_record_page' with schema.\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["create_collection_page_with_partitions()"]}, {"cell_type": "code", "execution_count": 23, "id": "caf12e93", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-22 19:10:32,974 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:32,997 - INFO - ✅ Created partition 'defaultsession_a2865c37'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-22 19:10:33,010 - INFO - 📌 Inserted session 'page_8edea251' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,018 - INFO - 📌 Inserted session 'page_e00a831c' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,024 - INFO - 📌 Inserted session 'page_d7cc1826' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,030 - INFO - 📌 Inserted session 'page_99f50f39' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,036 - INFO - 📌 Inserted session 'page_8f70da76' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,042 - INFO - 📌 Inserted session 'page_5c5cbbe7' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,049 - INFO - 📌 Inserted session 'page_cd23b0b4' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,055 - INFO - 📌 Inserted session 'page_7ebfe272' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,061 - INFO - 📌 Inserted session 'page_cdef4017' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,067 - INFO - 📌 Inserted session 'page_ed7d0405' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,073 - INFO - 📌 Inserted session 'page_6bbd1e89' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,079 - INFO - 📌 Inserted session 'page_d2f9a6bd' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,085 - INFO - 📌 Inserted session 'page_16284c8a' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,091 - INFO - 📌 Inserted session 'page_94746052' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,100 - INFO - 📌 Inserted session 'page_7dec7d3d' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,108 - INFO - 📌 Inserted session 'page_bd927579' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,116 - INFO - 📌 Inserted session 'page_70608c36' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,124 - INFO - 📌 Inserted session 'page_27496c20' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,130 - INFO - 📌 Inserted session 'page_b5fcd00f' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,136 - INFO - 📌 Inserted session 'page_af2c9823' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,141 - INFO - 📌 Inserted session 'page_386a44f7' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,147 - INFO - 📌 Inserted session 'page_fd55895b' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,154 - INFO - 📌 Inserted session 'page_5f0fe0be' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,160 - INFO - 📌 Inserted session 'page_1c9d5011' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,167 - INFO - 📌 Inserted session 'page_947ffb50' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,173 - INFO - 📌 Inserted session 'page_63b993dc' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,180 - INFO - 📌 Inserted session 'page_a49a795d' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,187 - INFO - 📌 Inserted session 'page_24bbb348' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,193 - INFO - 📌 Inserted session 'page_7c5971af' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,198 - INFO - 📌 Inserted session 'page_8e31488e' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,203 - INFO - 📌 Inserted session 'page_b3160a69' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,209 - INFO - 📌 Inserted session 'page_0ef5674e' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,215 - INFO - 📌 Inserted session 'page_b02c6c0c' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,222 - INFO - 📌 Inserted session 'page_61344b3e' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,232 - INFO - 📌 Inserted session 'page_dee2f41b' into partition 'defaultsession_a2865c37'\n", "2025-07-22 19:10:33,236 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,256 - INFO - ✅ Created partition 'defaultsession_49c122c1'\n", "2025-07-22 19:10:33,267 - INFO - 📌 Inserted session 'page_76f33019' into partition 'defaultsession_49c122c1'\n", "2025-07-22 19:10:33,273 - INFO - 📌 Inserted session 'page_e4d0f7f1' into partition 'defaultsession_49c122c1'\n", "2025-07-22 19:10:33,277 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,295 - INFO - ✅ Created partition 'defaultsession_0ac4d10b'\n", "2025-07-22 19:10:33,305 - INFO - 📌 Inserted session 'page_26f676c0' into partition 'defaultsession_0ac4d10b'\n", "2025-07-22 19:10:33,311 - INFO - 📌 Inserted session 'page_12b903cf' into partition 'defaultsession_0ac4d10b'\n", "2025-07-22 19:10:33,314 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,337 - INFO - ✅ Created partition 'defaultsession_650b7843'\n", "2025-07-22 19:10:33,346 - INFO - 📌 Inserted session 'page_06834fa5' into partition 'defaultsession_650b7843'\n", "2025-07-22 19:10:33,353 - INFO - 📌 Inserted session 'page_368b925e' into partition 'defaultsession_650b7843'\n", "2025-07-22 19:10:33,361 - INFO - 📌 Inserted session 'page_ef486e7e' into partition 'defaultsession_650b7843'\n", "2025-07-22 19:10:33,369 - INFO - 📌 Inserted session 'page_bdbe0e10' into partition 'defaultsession_650b7843'\n", "2025-07-22 19:10:33,373 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,399 - INFO - ✅ Created partition 'defaultsession_663bc43c'\n", "2025-07-22 19:10:33,412 - INFO - 📌 Inserted session 'page_06834fa5' into partition 'defaultsession_663bc43c'\n", "2025-07-22 19:10:33,420 - INFO - 📌 Inserted session 'page_368b925e' into partition 'defaultsession_663bc43c'\n", "2025-07-22 19:10:33,432 - INFO - 📌 Inserted session 'page_ef486e7e' into partition 'defaultsession_663bc43c'\n", "2025-07-22 19:10:33,435 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,455 - INFO - ✅ Created partition 'defaultsession_7bd094bd'\n", "2025-07-22 19:10:33,466 - INFO - 📌 Inserted session 'page_e4d0f7f1' into partition 'defaultsession_7bd094bd'\n", "2025-07-22 19:10:33,470 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,492 - INFO - ✅ Created partition 'defaultsession_bdce06eb'\n", "2025-07-22 19:10:33,504 - INFO - 📌 Inserted session 'page_428a908a' into partition 'defaultsession_bdce06eb'\n", "2025-07-22 19:10:33,512 - INFO - 📌 Inserted session 'page_8d03f331' into partition 'defaultsession_bdce06eb'\n", "2025-07-22 19:10:33,518 - INFO - 📌 Inserted session 'page_82a77618' into partition 'defaultsession_bdce06eb'\n", "2025-07-22 19:10:33,525 - INFO - 📌 Inserted session 'page_5ee8c752' into partition 'defaultsession_bdce06eb'\n", "2025-07-22 19:10:33,528 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,550 - INFO - ✅ Created partition 'defaultsession_ece1a812'\n", "2025-07-22 19:10:33,560 - INFO - 📌 Inserted session 'page_428a908a' into partition 'defaultsession_ece1a812'\n", "2025-07-22 19:10:33,564 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,587 - INFO - ✅ Created partition 'defaultsession_c5dd398d'\n", "2025-07-22 19:10:33,597 - INFO - 📌 Inserted session 'page_212be807' into partition 'defaultsession_c5dd398d'\n", "2025-07-22 19:10:33,605 - INFO - 📌 Inserted session 'page_d8dcc0e7' into partition 'defaultsession_c5dd398d'\n", "2025-07-22 19:10:33,613 - INFO - 📌 Inserted session 'page_43c9ce04' into partition 'defaultsession_c5dd398d'\n", "2025-07-22 19:10:33,617 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,637 - INFO - ✅ Created partition 'defaultsession_566c59c8'\n", "2025-07-22 19:10:33,653 - INFO - 📌 Inserted session 'page_212be807' into partition 'defaultsession_566c59c8'\n", "2025-07-22 19:10:33,660 - INFO - 📌 Inserted session 'page_d8dcc0e7' into partition 'defaultsession_566c59c8'\n", "2025-07-22 19:10:33,663 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,686 - INFO - ✅ Created partition 'defaultsession_623c8e67'\n", "2025-07-22 19:10:33,696 - INFO - 📌 Inserted session 'page_fbaeaf3b' into partition 'defaultsession_623c8e67'\n", "2025-07-22 19:10:33,703 - INFO - 📌 Inserted session 'page_fbaeaf3b' into partition 'defaultsession_623c8e67'\n", "2025-07-22 19:10:33,707 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,730 - INFO - ✅ Created partition 'defaultsession_3f559fc2'\n", "2025-07-22 19:10:33,742 - INFO - 📌 Inserted session 'page_8d03f331' into partition 'defaultsession_3f559fc2'\n", "2025-07-22 19:10:33,751 - INFO - 📌 Inserted session 'page_82a77618' into partition 'defaultsession_3f559fc2'\n", "2025-07-22 19:10:33,759 - INFO - 📌 Inserted session 'page_5ee8c752' into partition 'defaultsession_3f559fc2'\n", "2025-07-22 19:10:33,762 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,784 - INFO - ✅ Created partition 'defaultsession_ead26588'\n", "2025-07-22 19:10:33,799 - INFO - 📌 Inserted session 'page_8f80e9ec' into partition 'defaultsession_ead26588'\n", "2025-07-22 19:10:33,804 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,830 - INFO - ✅ Created partition 'defaultsession_0ce42213'\n", "2025-07-22 19:10:33,843 - INFO - 📌 Inserted session 'page_8f80e9ec' into partition 'defaultsession_0ce42213'\n", "2025-07-22 19:10:33,846 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,866 - INFO - ✅ Created partition 'defaultsession_cac6cec4'\n", "2025-07-22 19:10:33,877 - INFO - 📌 Inserted session 'page_e64525a0' into partition 'defaultsession_cac6cec4'\n", "2025-07-22 19:10:33,885 - INFO - 📌 Inserted session 'page_326cb63d' into partition 'defaultsession_cac6cec4'\n", "2025-07-22 19:10:33,892 - INFO - 📌 Inserted session 'page_fcc3b7c1' into partition 'defaultsession_cac6cec4'\n", "2025-07-22 19:10:33,900 - INFO - 📌 Inserted session 'page_3d402d9e' into partition 'defaultsession_cac6cec4'\n", "2025-07-22 19:10:33,906 - INFO - 📌 Inserted session 'page_b5e3e36e' into partition 'defaultsession_cac6cec4'\n", "2025-07-22 19:10:33,913 - INFO - 📌 Inserted session 'page_d172e23a' into partition 'defaultsession_cac6cec4'\n", "2025-07-22 19:10:33,920 - INFO - 📌 Inserted session 'page_77f80091' into partition 'defaultsession_cac6cec4'\n", "2025-07-22 19:10:33,927 - INFO - 📌 Inserted session 'page_d1185c6e' into partition 'defaultsession_cac6cec4'\n", "2025-07-22 19:10:33,934 - INFO - 📌 Inserted session 'page_d1185c6e' into partition 'defaultsession_cac6cec4'\n", "2025-07-22 19:10:33,943 - INFO - 📌 Inserted session 'page_0df41f95' into partition 'defaultsession_cac6cec4'\n", "2025-07-22 19:10:33,951 - INFO - 📌 Inserted session 'page_2dce7803' into partition 'defaultsession_cac6cec4'\n", "2025-07-22 19:10:33,954 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:33,974 - INFO - ✅ Created partition 'defaultsession_4676398f'\n", "2025-07-22 19:10:33,986 - INFO - 📌 Inserted session 'page_d7e11335' into partition 'defaultsession_4676398f'\n", "2025-07-22 19:10:33,994 - INFO - 📌 Inserted session 'page_b5e3e36e' into partition 'defaultsession_4676398f'\n", "2025-07-22 19:10:33,997 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:34,020 - INFO - ✅ Created partition 'defaultsession_9cfe1668'\n", "2025-07-22 19:10:34,042 - INFO - 📌 Inserted session 'page_d7e11335' into partition 'defaultsession_9cfe1668'\n", "2025-07-22 19:10:34,046 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:34,069 - INFO - ✅ Created partition 'defaultsession_ab4a0f29'\n", "2025-07-22 19:10:34,079 - INFO - 📌 Inserted session 'page_12b903cf' into partition 'defaultsession_ab4a0f29'\n", "2025-07-22 19:10:34,082 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:34,175 - INFO - ✅ Created partition 'defaultsession_ec63feb3'\n", "2025-07-22 19:10:34,185 - INFO - 📌 Inserted session 'page_bdbe0e10' into partition 'defaultsession_ec63feb3'\n", "2025-07-22 19:10:34,191 - INFO - 📌 Inserted session 'page_30bb3a2c' into partition 'defaultsession_ec63feb3'\n", "2025-07-22 19:10:34,197 - INFO - 📌 Inserted session 'page_30bb3a2c' into partition 'defaultsession_ec63feb3'\n", "2025-07-22 19:10:34,200 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:34,220 - INFO - ✅ Created partition 'defaultsession_7ff70bb3'\n", "2025-07-22 19:10:34,230 - INFO - 📌 Inserted session 'page_974d669d' into partition 'defaultsession_7ff70bb3'\n", "2025-07-22 19:10:34,233 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:34,251 - INFO - ✅ Created partition 'defaultsession_897f9bec'\n", "2025-07-22 19:10:34,261 - INFO - 📌 Inserted session 'page_d70886b4' into partition 'defaultsession_897f9bec'\n", "2025-07-22 19:10:34,266 - INFO - 📌 Inserted session 'page_d70886b4' into partition 'defaultsession_897f9bec'\n", "2025-07-22 19:10:34,269 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:34,287 - INFO - ✅ Created partition 'defaultsession_5fa7f22a'\n", "2025-07-22 19:10:34,296 - INFO - 📌 Inserted session 'page_43c9ce04' into partition 'defaultsession_5fa7f22a'\n", "2025-07-22 19:10:34,303 - INFO - 📌 Inserted session 'page_6d34593e' into partition 'defaultsession_5fa7f22a'\n", "2025-07-22 19:10:34,309 - INFO - 📌 Inserted session 'page_6d34593e' into partition 'defaultsession_5fa7f22a'\n", "2025-07-22 19:10:34,312 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:34,331 - INFO - ✅ Created partition 'defaultsession_0b7eb729'\n", "2025-07-22 19:10:34,340 - INFO - 📌 Inserted session 'page_0f9424c7' into partition 'defaultsession_0b7eb729'\n", "2025-07-22 19:10:34,346 - INFO - 📌 Inserted session 'page_9cccbb3b' into partition 'defaultsession_0b7eb729'\n", "2025-07-22 19:10:34,348 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:34,368 - INFO - ✅ Created partition 'defaultsession_de7aaf02'\n", "2025-07-22 19:10:34,377 - INFO - 📌 Inserted session 'page_0f9424c7' into partition 'defaultsession_de7aaf02'\n", "2025-07-22 19:10:34,380 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:34,401 - INFO - ✅ Created partition 'defaultsession_a4e196b5'\n", "2025-07-22 19:10:34,413 - INFO - 📌 Inserted session 'page_6e82d6c6' into partition 'defaultsession_a4e196b5'\n", "2025-07-22 19:10:34,420 - INFO - 📌 Inserted session 'page_6e82d6c6' into partition 'defaultsession_a4e196b5'\n", "2025-07-22 19:10:34,428 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:34,450 - INFO - ✅ Created partition 'defaultsession_e2a926ad'\n", "2025-07-22 19:10:34,462 - INFO - 📌 Inserted session 'page_9cccbb3b' into partition 'defaultsession_e2a926ad'\n", "2025-07-22 19:10:34,465 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:34,489 - INFO - ✅ Created partition 'defaultsession_3d5219e0'\n", "2025-07-22 19:10:34,500 - INFO - 📌 Inserted session 'page_6af72b31' into partition 'defaultsession_3d5219e0'\n", "2025-07-22 19:10:34,504 - INFO - ⚠️ 分区不存在，请创建分区!\n", "2025-07-22 19:10:34,534 - INFO - ✅ Created partition 'defaultsession_6f26bf60'\n", "2025-07-22 19:10:34,546 - INFO - 📌 Inserted session 'page_d19a3c98' into partition 'defaultsession_6f26bf60'\n", "2025-07-22 19:10:34,554 - INFO - 📌 Inserted session 'page_d19a3c98' into partition 'defaultsession_6f26bf60'\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["insert_page_embeddings_by_user(user_id=\"default\", data=data_to_insert)"]}, {"cell_type": "code", "execution_count": 25, "id": "03ef63ae", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-22 19:11:04,559 - INFO - 🔄 Deleted session '['page_16284c8a']' in partition 'defaultsession_a2865c37'\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["delete_page_embeddings_by_user(user_id=\"default\", session_id=\"session_a2865c37\", page_ids=[\"page_16284c8a\"], clear_all=False)"]}, {"cell_type": "code", "execution_count": 14, "id": "49bd61a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Found top 5 similar sessions in partition 'defaultsession_a2865c37':\n"]}, {"data": {"text/plain": ["[{'output': {'id': 'page_e00a831c',\n", "   'user_input': '',\n", "   'agent_response': '',\n", "   'timestamp': '',\n", "   'preloaded': <PERSON><PERSON><PERSON>,\n", "   'analyzed': <PERSON><PERSON><PERSON>,\n", "   'pre_page': 'default',\n", "   'next_page': 'default',\n", "   'meta_info': '',\n", "   'page_embedding': []},\n", "  'distance': 41.900978088378906},\n", " {'output': {'id': 'page_7c5971af',\n", "   'user_input': '',\n", "   'agent_response': '',\n", "   'timestamp': '',\n", "   'preloaded': <PERSON><PERSON><PERSON>,\n", "   'analyzed': <PERSON><PERSON><PERSON>,\n", "   'pre_page': 'default',\n", "   'next_page': 'default',\n", "   'meta_info': '',\n", "   'page_embedding': []},\n", "  'distance': 42.15991973876953},\n", " {'output': {'id': 'page_27496c20',\n", "   'user_input': '',\n", "   'agent_response': '',\n", "   'timestamp': '',\n", "   'preloaded': <PERSON><PERSON><PERSON>,\n", "   'analyzed': <PERSON><PERSON><PERSON>,\n", "   'pre_page': 'default',\n", "   'next_page': 'default',\n", "   'meta_info': '',\n", "   'page_embedding': []},\n", "  'distance': 42.242149353027344},\n", " {'output': {'id': 'page_1c9d5011',\n", "   'user_input': '',\n", "   'agent_response': '',\n", "   'timestamp': '',\n", "   'preloaded': <PERSON><PERSON><PERSON>,\n", "   'analyzed': <PERSON><PERSON><PERSON>,\n", "   'pre_page': 'default',\n", "   'next_page': 'default',\n", "   'meta_info': '',\n", "   'page_embedding': []},\n", "  'distance': 42.25019073486328},\n", " {'output': {'id': 'page_b5fcd00f',\n", "   'user_input': '',\n", "   'agent_response': '',\n", "   'timestamp': '',\n", "   'preloaded': <PERSON><PERSON><PERSON>,\n", "   'analyzed': <PERSON><PERSON><PERSON>,\n", "   'pre_page': 'default',\n", "   'next_page': 'default',\n", "   'meta_info': '',\n", "   'page_embedding': []},\n", "  'distance': 42.35738754272461}]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["search_similar_pages(user_id=\"default\", session_id=\"session_a2865c37\", query_embedding=[0.2]*1024)"]}], "metadata": {"kernelspec": {"display_name": "MACRec", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}