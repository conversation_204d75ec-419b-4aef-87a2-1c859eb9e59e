#---------------#
# 数据库封装
#---------------#

from config.database_config import *
from mysql.connector import Error, errorcode

import sys
import os
import mysql.connector
import logging


# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class MySQLDB:
    def __init__(self, 
                 host:str=MYSQL_HOST, 
                 user:str=MYSQL_USER, 
                 password:str=MYSQL_PASSWORD, 
                 database:str=MYSQL_DATABASE, 
                 port:int=MYSQL_PORT):
        """
        初始化数据库连接参数
        :param host: 数据库主机地址
        :param user: 用户名
        :param password: 密码
        :param database: 默认数据库
        :param port: 端口号，默认为3306
        """
        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.port = port
        self.connection = None
        self.connect()

    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                port=self.port
            )
            if self.connection.is_connected():
                logging.info("✅ 成功连接到数据库")
        except Error as e:
            if e.errno == errorcode.ER_BAD_DB_ERROR:
                logging.warning("⚠️ 数据库不存在，尝试自动创建...")
                self._create_database()
                self.connect()  # 重新连接
            else:
                logging.error(f"❌ 数据库连接失败: {e}")
                raise

    def _create_database(self):
        """如果数据库不存在，则自动创建"""
        temp_conn = mysql.connector.connect(
            host=self.host,
            user=self.user,
            password=self.password,
            port=self.port
        )
        cursor = temp_conn.cursor()
        try:
            cursor.execute(f"CREATE DATABASE {self.database}")
            logging.info(f"🆕 数据库 {self.database} 已创建")
        except Error as e:
            logging.error(f"❌ 创建数据库失败: {e}")
            raise
        finally:
            cursor.close()
            temp_conn.close()

    def close(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logging.info("🔌 数据库连接已关闭")

    def execute_sql(self, sql: str, params=None):
        """
        执行任意 SQL 语句（通用）
        :param sql: 要执行的 SQL 语句
        :param params: 参数化查询参数
        :return: 受影响行数或查询结果
        """
        with self.connection.cursor(buffered=True, dictionary=True) as cursor:
            try:
                cursor.execute(sql, params or ())
                if sql.strip().lower().startswith('select'):
                    result = cursor.fetchall()
                    return result
                else:
                    self.connection.commit()
                    return cursor.rowcount
            except Error as e:
                logging.error(f"❌ SQL执行失败: {e} | SQL: {sql} | Params: {params}")
                self.connection.rollback()
                return False

    def create_table(self, table_name: str=None, fields: dict = None, sql: str = None):
        if table_name == None:
            logging.info(f"⚠️ 没有给出table名称，行动终止！")
            return None
        """
        创建数据表
        :param table_name: 表名
        :param fields: 字段定义字典（如 {'id': 'INT PRIMARY KEY AUTO_INCREMENT', 'name': 'VARCHAR(100)'}）
        :param sql: 完整的 CREATE TABLE 语句（优先使用）
        :return: 成功返回 True，失败返回 False
        """
        if sql:
            create_sql = sql
        elif fields:
            field_sql = ', '.join([f"`{k}` {v}" for k, v in fields.items()])
            create_sql = f"CREATE TABLE IF NOT EXISTS `{table_name}` ({field_sql})"
        else:
            logging.warning("❌ 缺少建表参数，请提供 fields 或 sql")
            return False

        logging.info(f"🛠️ 正在执行建表语句: {create_sql}")
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(create_sql)
                self.connection.commit()
                logging.info(f"🆕 表 {table_name} 已创建或已存在")
                return True
        except Error as e:
            logging.error(f"❌ 建表失败: {e} | SQL: {create_sql}")
            self.connection.rollback()
            return False

    # 插入记录(这里请注意，因为插入的sql函数是需要key和value对应的，所以我们规定了data必须用dict来进行存储)
    def insert(self, table: str=None, data: dict=None):
        """
        插入一条记录
        :param table: 表名
        :param data: 字典形式的数据 {字段: 值}
        :return: 影响行数 或 False
        """
        if table == None or data==None:
            logging.info(f"⚠️ 重要插入信息缺乏，行动终止！")
            return None
        
        keys = ', '.join(data.keys())
        values = list(data.values())
        placeholders = ', '.join(['%s'] * len(data))
        sql = f"INSERT INTO {table} ({keys}) VALUES ({placeholders})"
        return self.execute_sql(sql, tuple(values))

    # 更新记录
    def update(self, table: str=None, data: dict=None, where: str=None, params: tuple = None):
        """
        更新记录
        :param table: 表名
        :param data: 要更新的字段和值 {字段: 值}
        :param where: WHERE 条件语句（如 id=%s）
        :param params: WHERE 中的参数元组
        :return: 影响行数 或 False
        """
        if table == None or data==None or where==None:
            logging.info(f"⚠️ 重要更新信息缺乏，行动终止！")
            return None
        
        set_clause = ', '.join([f"{key} = %s" for key in data.keys()])
        values = list(data.values())
        if params:
            values.extend(params)
        sql = f"UPDATE {table} SET {set_clause} WHERE {where}"
        return self.execute_sql(sql, tuple(values))
    
    # 删除记录
    def delete(self, table: str=None, where: str=None, params: tuple = None):
        """
        删除记录
        :param table: 表名
        :param where: WHERE 条件语句
        :param params: WHERE 参数
        :return: 影响行数 或 False
        """
        if table == None or where==None:
            logging.info(f"⚠️ 重要删除信息缺乏，行动终止！")
            return None
        sql = f"DELETE FROM {table} WHERE {where}"
        return self.execute_sql(sql, params)

    # 查询记录
    def select(self, table: str=None, columns:str='*', where: str = None, params: tuple = None, order_by: str = None, ascending: bool = True, fetch:int=None):
        """
       查询记录，支持排序
        :param table: 表名
        :param columns: 要查询的列，默认是 *
        :param where: WHERE 条件
        :param params: WHERE 参数
        :param order_by: 排序列名（如 'timestamp'）
        :param ascending: 是否升序（True=升序，False=降序）
        :param fetch: 取多少行
        :return: 查询结果列表 或 单条记录
        """
        if table == None:
            logging.info(f"⚠️ 没有给出table名称，行动终止！")
            return None
        sql = f"SELECT {columns} FROM {table}"
        if where:
            sql += f" WHERE {where}"
        if order_by:
            direction = "ASC" if ascending else "DESC"
            sql += f" ORDER BY {order_by} {direction}"
    
        result = self.execute_sql(sql, params)

        if len(result) == 0:
            logging.info(f"🔍 查询结果为空，SQL: {sql} | Params: {params}")
            return []
        
        # 不设置的情况下直接返回所有检索到的内容
        if fetch == None:
            return result
    
        if fetch >= len(result):
            logging.warning(f"⚠️ 取出的记录数 {len(result)} 少于请求的 {fetch} 条，返回全部结果")
            return result
        elif fetch < len(result) and fetch >= 0:
            return result[:fetch] if result else None
        elif fetch < 0:
            logging.warning("⚠️ fetch 参数不能为负数，返回全部结果")
            return result
        
        return result
    
    # 删除指定表
    def drop_table(self, table_name: str=None):
        """
        删除指定表
        :param table_name: 表名
        :return: 成功返回 True，失败返回 False
        """
        if table_name == None:
            logging.info(f"⚠️ 没有给出table名称，行动终止！")
            return None
        sql = f"DROP TABLE IF EXISTS `{table_name}`"
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                self.connection.commit()
                logging.info(f"🗑️ 表 {table_name} 已删除")
                return True
        except Error as e:
            logging.error(f"❌ 删除表失败: {e} | SQL: {sql}")
            self.connection.rollback()
            return False
    
    # 检查表是否存在
    def table_exists(self, table_name):
        """
        检查表是否存在
        param:table_name:表的名称
        """

        if table_name == None:
            logging.info(f"⚠️ 没有给出table名称，行动终止！")
            return None
        
        sql = f"SHOW TABLES LIKE '{table_name}'"

        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                self.connection.commit()
                logging.info(f"表 {table_name} 存在")
                return True
        except Error as e:
            logging.error(f"表 {table_name} 不存在")
            self.connection.rollback()
            return False

    
    # 重命名数据表
    def rename_table(self, old_name: str=None, new_name: str=None):
        """
        重命名数据表
        :param old_name: 原表名
        :param new_name: 新表名
        :return: 成功返回 True，失败返回 False
        """
        if old_name == None or new_name==None:
            logging.info(f"⚠️ 重要信息缺乏，行动终止！")
            return None
        sql = f"RENAME TABLE `{old_name}` TO `{new_name}`"
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                self.connection.commit()
                logging.info(f"🔄 表 {old_name} 已重命名为 {new_name}")
                return True
        except Error as e:
            logging.error(f"❌ 重命名表失败: {e} | SQL: {sql}")
            self.connection.rollback()
            return False
    
    # 对表新增列
    def add_column(self, table_name: str=None, column_name: str=None, definition: str=None):
        """
        向表中添加字段
        :param table_name: 表名
        :param column_name: 新字段名
        :param definition: 字段定义，如 "VARCHAR(100) DEFAULT NULL"
        :return: 成功返回 True，失败返回 False
        """
        if table_name == None or column_name==None or definition==None:
            logging.info(f"⚠️ 重要信息缺乏，行动终止！")
            return None
        
        sql = f"ALTER TABLE `{table_name}` ADD COLUMN `{column_name}` {definition}"
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                self.connection.commit()
                logging.info(f"🆕 字段 {column_name} 已添加到表 {table_name}")
                return True
        except Error as e:
            logging.error(f"❌ 添加字段失败: {e} | SQL: {sql}")
            self.connection.rollback()
            return False

    # 删除列
    def drop_column(self, table_name: str=None, column_name: str=None):
        """
        删除表中的字段
        :param table_name: 表名
        :param column_name: 要删除的字段名
        :return: 成功返回 True，失败返回 False
        """
        if table_name == None or column_name==None:
            logging.info(f"⚠️ 重要信息缺乏，行动终止！")
            return None
        
        sql = f"ALTER TABLE `{table_name}` DROP COLUMN `{column_name}`"
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                self.connection.commit()
                logging.info(f"🗑️ 字段 {column_name} 已从表 {table_name} 中删除")
                return True
        except Error as e:
            logging.error(f"❌ 删除字段失败: {e} | SQL: {sql}")
            self.connection.rollback()
            return False
        
    # 修改表中字段的定义
    def modify_column(self, table_name: str=None, old_name: str=None, new_definition: str=None):
        """
        修改字段定义（保留字段名）
        :param table_name: 表名
        :param old_name: 字段名
        :param new_definition: 新的字段定义，如 "VARCHAR(255) NOT NULL"
        :return: 成功返回 True，失败返回 False
        """
        if table_name == None or old_name==None or new_definition==None:
            logging.info(f"⚠️ 重要信息缺乏，行动终止！")
            return None
        
        sql = f"ALTER TABLE `{table_name}` MODIFY `{old_name}` {new_definition}"
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                self.connection.commit()
                logging.info(f"🛠️ 字段 {old_name} 的定义已修改为 {new_definition}")
                return True
        except Error as e:
            logging.error(f"❌ 修改字段定义失败: {e} | SQL: {sql}")
            self.connection.rollback()
            return False
    
    # 修改表名称以及字段定义
    def change_column(self, table_name: str=None, old_name: str=None, new_name: str=None, new_definition: str=None):
        """
        修改字段名和定义
        :param table_name: 表名
        :param old_name: 原字段名
        :param new_name: 新字段名
        :param new_definition: 新的字段定义
        :return: 成功返回 True，失败返回 False
        """
        if table_name == None or old_name==None or new_name==None or new_definition==None:
            logging.info(f"⚠️ 重要信息缺乏，行动终止！")
            return None
        
        sql = f"ALTER TABLE `{table_name}` CHANGE `{old_name}` `{new_name}` {new_definition}"
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                self.connection.commit()
                logging.info(f"🔄 字段 {old_name} 已重命名为 {new_name} 并修改定义为 {new_definition}")
                return True
        except Error as e:
            logging.error(f"❌ 修改字段名和定义失败: {e} | SQL: {sql}")
            self.connection.rollback()
            return False

    # 自定义sql查询
    def query(self, sql: str, params=None):
        """
        自定义 SQL 查询
        :param sql: 完整的 SQL 语句
        :param params: 参数化参数
        :return: 查询结果（如果是 SELECT）
        """
        return self.execute_sql(sql, params)

    # 自定义sql执行
    def execute(self, sql: str, params=None):
        """
        自定义 SQL 执行（用于 INSERT/UPDATE/DELETE）
        :param sql: 完整的 SQL 语句
        :param params: 参数化参数
        :return: 影响行数 或 False
        """
        return self.execute_sql(sql, params)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()