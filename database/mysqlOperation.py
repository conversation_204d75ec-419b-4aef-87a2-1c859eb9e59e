#---------------#
# 适配short_term的操作
#---------------#

from MySQLDB import MySQLDB
import logging
# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# 根据数据特征创建表（本函数不应放入memos中，但一定要确保表存在）
def create_short_term_table(table_name:str=None) -> bool:
    """
    input:
        table_name:表的名称
    """
    # 判定数据完整性
    if table_name == None:
        logging.info("⚠️ 没有给出要创建的表名称")
        return False

    # 初始化数据库,数据库参数都是在config中调整
    try:
        db = MySQLDB()
    except Exception as e:
        logging.info("❌ 数据库连接失败:", e)
        return False

    # 执行创建表的语句（这里使用的是create_table函数中直接输入sql的方式）
    create_sql = f"""
    CREATE TABLE IF NOT EXISTS {table_name} (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id VARCHAR(50) NOT NULL,
        user_input TEXT NOT NULL,
        agent_response TEXT NOT NULL,
        `timestamp` DATETIME NOT NULL,
        INDEX idx_user_id (user_id),
        INDEX idx_timestamp (`timestamp`)
    )
    """
    # 判断表是否存在，我们认为存在的表一定是我们sql语句定义的表
    if db.table_exists(table_name):
        return True
    else:
        # 创建表
        try:
            db.create_table(table_name, sql=create_sql)
            return True
        except Exception as e:
            logging.info("❌ 短期记忆表创建表格失败:", e)
            return False

# 保存short_term中关于用户的信息
def save_short_term_to_mysql(user_id:str=None, data:list[dict, any]=None, table_name:str="short_term_record") -> bool:
    """
    input:
        user_id:用户的id
        data:要存入的data
        table_name:需要存储的表的名称（适配insert和delete）
    """
    # 初始化数据库,数据库参数都是在config中调整
    try:
        db = MySQLDB()
    except Exception as e:
        logging.info("❌ 数据库连接失败:", e)
        return False
    
    # 判定数据完整性
    if user_id == None:
        logging.info("⚠️ 没有给出用户的id号")
        return False
    
    if data == None:
        logging.info("⚠️ 没有给出有效的对话数据，存储过程跳过")
        return False
    
    # 删除该用户id下保存在数据库中的所有内容
    try:
        where = "user_id = %s"
        params = (user_id,)
        db.delete(table=table_name, where=where, params=params)
        logging.info("✅ 删除成功")
    except Exception as e:
        logging.info("⚠️ 删除过程存在问题！")
        return False
    
    # 插入新的对话数据
    try:
        for item in data:
            record = {
                'user_id': user_id,
                'user_input': item['user_input'],
                'agent_response': item['agent_response'],
                'timestamp': item['timestamp']
            }
            rows = db.insert(table=table_name, data=record)
        logging.info("✅ 插入记录成功!")
        return True
    except Exception as e:
        logging.info("⚠️ 插入数据过程存在问题！")
        return False

# 读取user对应的短期记忆的数据
def load_short_term_from_mysql(user_id:str=None, table_name:str="short_term_record") -> list[dict, any]:
    """
    input:
        user_id:用户的id
        table_name:表的名称
    return:
        data:对应的短期对话数据
    """
    # 初始化数据库,数据库参数都是在config中调整
    try:
        db = MySQLDB()
    except Exception as e:
        logging.info("❌ 数据库连接失败:", e)
        return False

    # 判定数据完整性
    if user_id == None:
        logging.info("⚠️ 没有给出用户的id号")
        return False
    
    # 直接读取需要的数据库内容，并且按照时间进行排序
    try:
        records = db.select(
            table=table_name,
            where='user_id=%s',
            params=(user_id,),
            order_by='timestamp',
            ascending=True  # 从早到晚排序
        )
        datas = []
        for record in records:
            data = {
                'user_input': record['user_input'],
                'agent_response': record['agent_response'],
                'timestamp': record['timestamp'].strftime("%Y-%m-%d %H:%M:%S")
            }
            datas.append(data)
        logging.info("✅ 读取成功!")
        return datas
    except:
        logging.info("⚠️ 读取失败！")
        return []

    







