{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4b990188", "metadata": {}, "outputs": [], "source": ["import unittest\n", "from MySQLDB import MySQLDB\n", "# 在 notebook 的 cell 中执行以下代码：\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "class TestMySQLDB(unittest.TestCase):\n", "    @classmethod\n", "    def setUpClass(cls):\n", "        \"\"\"初始化数据库连接（用于所有测试）\"\"\"\n", "        cls.db = MySQLDB()\n", "\n", "    @classmethod\n", "    def tearDownClass(cls):\n", "        \"\"\"清理数据库（可选）\"\"\"\n", "        with cls.db.connection.cursor() as cursor:\n", "            cursor.execute(\"DROP DATABASE IF EXISTS test_mydb\")\n", "        cls.db.close()\n", "\n", "    def test_01_connect(self):\n", "        \"\"\"测试数据库连接是否成功\"\"\"\n", "        self.assertTrue(self.db.connection.is_connected())\n", "\n", "    def test_02_create_table_by_fields(self):\n", "        \"\"\"测试通过字段定义创建表\"\"\"\n", "        fields = {\n", "            'id': 'INT PRIMARY KEY AUTO_INCREMENT',\n", "            'name': 'VARCHAR(100)',\n", "            'age': 'INT',\n", "            'email': 'VARCHAR(100)'\n", "        }\n", "        result = self.db.create_table('users', fields=fields)\n", "        self.assertTrue(result)\n", "\n", "    def test_03_create_table_by_sql(self):\n", "        \"\"\"测试通过完整 SQL 创建表\"\"\"\n", "        create_sql = \"\"\"\n", "        CREATE TABLE IF NOT EXISTS orders (\n", "            order_id INT PRIMARY KEY AUTO_INCREMENT,\n", "            user_id INT,\n", "            amount DECIMAL(10,2),\n", "            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n", "        )\n", "        \"\"\"\n", "        result = self.db.create_table('orders', sql=create_sql)\n", "        self.assertTrue(result)\n", "\n", "    def test_04_insert(self):\n", "        \"\"\"测试插入数据\"\"\"\n", "        data = {'name': '张三', 'age': 25, 'email': 'zhang<PERSON>@example.com'}\n", "        rows = self.db.insert('users', data)\n", "        self.assertEqual(rows, 1)\n", "\n", "    def test_05_select(self):\n", "        \"\"\"测试查询数据\"\"\"\n", "        result = self.db.select('users', where='name=%s', params=('张三',), fetch=1)\n", "        print(result)\n", "\n", "    def test_06_update(self):\n", "        \"\"\"测试更新数据\"\"\"\n", "        data = {'age': 26}\n", "        rows = self.db.update('users', data, 'name=%s', ('张三',))\n", "\n", "        result = self.db.select('users', where='name=%s', params=('张三',), fetch=1)\n", "        print(result)\n", "\n", "    def test_07_delete(self):\n", "        \"\"\"测试删除数据\"\"\"\n", "        rows = self.db.delete('users', 'name=%s', ('张三',))\n", "        self.assertEqual(rows, 1)\n", "\n", "        result = self.db.select('users', where='name=%s', params=('张三',))\n", "        self.assertEqual(len(result), 0)\n", "\n", "    def test_08_with_context_manager(self):\n", "        \"\"\"测试 with 上下文管理器自动连接与关闭\"\"\"\n", "        with MySQLDB() as db:\n", "            self.assertTrue(db.connection.is_connected())\n", "            result = db.select('users')\n", "            self.assertIsInstance(result, list)\n", "\n", "        # 离开 with 后应已自动关闭连接\n", "        self.assertFalse(db.connection.is_connected())"]}, {"cell_type": "code", "execution_count": 2, "id": "067c1dd2", "metadata": {}, "outputs": [], "source": ["test = TestMySQLDB()"]}, {"cell_type": "code", "execution_count": 3, "id": "341fdd1b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-22 19:16:49,357 - INFO - package: mysql.connector.plugins\n", "2025-07-22 19:16:49,358 - INFO - plugin_name: caching_sha2_password\n", "2025-07-22 19:16:49,359 - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin\n", "2025-07-22 19:16:49,364 - WARNING - ⚠️ 数据库不存在，尝试自动创建...\n", "2025-07-22 19:16:49,461 - INFO - 🆕 数据库 testlocal 已创建\n", "2025-07-22 19:16:49,575 - INFO - ✅ 成功连接到数据库\n"]}], "source": ["test.setUpClass()"]}, {"cell_type": "code", "execution_count": 4, "id": "bb820ffb", "metadata": {}, "outputs": [], "source": ["test.test_01_connect()"]}, {"cell_type": "code", "execution_count": 5, "id": "78ad7ab6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-22 19:16:53,018 - INFO - 🛠️ 正在执行建表语句: CREATE TABLE IF NOT EXISTS `users` (`id` INT PRIMARY KEY AUTO_INCREMENT, `name` VARCHAR(100), `age` INT, `email` VARCHAR(100))\n", "2025-07-22 19:16:53,057 - INFO - 🆕 表 users 已创建或已存在\n"]}], "source": ["test.test_02_create_table_by_fields()"]}, {"cell_type": "code", "execution_count": 6, "id": "1342267e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-22 19:16:54,911 - INFO - 🛠️ 正在执行建表语句: \n", "        CREATE TABLE IF NOT EXISTS orders (\n", "            order_id INT PRIMARY KEY AUTO_INCREMENT,\n", "            user_id INT,\n", "            amount DECIMAL(10,2),\n", "            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n", "        )\n", "        \n", "2025-07-22 19:16:54,946 - INFO - 🆕 表 orders 已创建或已存在\n"]}], "source": ["test.test_03_create_table_by_sql()"]}, {"cell_type": "code", "execution_count": 7, "id": "6e08d0dd", "metadata": {}, "outputs": [], "source": ["test.test_04_insert()"]}, {"cell_type": "code", "execution_count": 8, "id": "41436bc3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-22 19:16:58,195 - WARNING - ⚠️ 取出的记录数 1 少于请求的 1 条，返回全部结果\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[{'id': 1, 'name': '张三', 'age': 25, 'email': 'z<PERSON><PERSON>@example.com'}]\n"]}], "source": ["test.test_05_select()"]}, {"cell_type": "code", "execution_count": 9, "id": "c18d22a8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-22 19:17:00,190 - WARNING - ⚠️ 取出的记录数 1 少于请求的 1 条，返回全部结果\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[{'id': 1, 'name': '张三', 'age': 26, 'email': 'z<PERSON><PERSON>@example.com'}]\n"]}], "source": ["test.test_06_update()"]}, {"cell_type": "code", "execution_count": 10, "id": "3342f089", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-22 19:17:01,427 - INFO - 🔍 查询结果为空，SQL: SELECT * FROM users WHERE name=%s | Params: ('张三',)\n"]}], "source": ["test.test_07_delete()"]}, {"cell_type": "code", "execution_count": 11, "id": "bbd287e9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-22 19:17:03,469 - INFO - ✅ 成功连接到数据库\n", "2025-07-22 19:17:03,472 - INFO - 🔍 查询结果为空，SQL: SELECT * FROM users | Params: None\n", "2025-07-22 19:17:03,473 - INFO - 🔌 数据库连接已关闭\n"]}], "source": ["test.test_08_with_context_manager()"]}, {"cell_type": "code", "execution_count": 12, "id": "d62c9f6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["# 在 notebook 的 cell 中执行以下代码：\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# 测试实际应用案例\n", "from mysqlOperation import *\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "c7245ca5", "metadata": {}, "outputs": [], "source": ["# 测试实际应用案例\n", "from mysqlOperation import *\n", "import json\n", "\n", "# 模拟数据\n", "chat_data = [\n", "    {\n", "        \"user_input\": \"行，这懂 啊，我现在就去，去看看江苏足球联赛\",\n", "        \"agent_response\": \"哈哈，江苏队最近状态不错啊！记得带点零食去看球~(25字)\",\n", "        \"timestamp\": \"2025-07-14 13:23:52\"\n", "    },\n", "    {\n", "        \"user_input\": \"上次说的吴京演的电影叫什么来着\",\n", "        \"agent_response\": \"吴京的《战狼2》超燃！还有《流浪地球》也很棒~(25字) (Time: 2025-07-14 13:24:30)\",\n", "        \"timestamp\": \"2025-07-14 13:24:55\"\n", "    },\n", "    {\n", "        \"user_input\": \"你记吴京演出的电影叫什么\",\n", "        \"agent_response\": \"吴京主演的《长津湖》超震撼！还有《战狼》系列也超经典~(25字)\",\n", "        \"timestamp\": \"2025-07-14 13:25:33\"\n", "    },\n", "    {\n", "        \"user_input\": \"这个不我们之前聊过的，我想问的是，之前我们聊过的吴京和刘德华一起演的电影名称\",\n", "        \"agent_response\": \"啊对！是《流浪地球2》，吴京和刘德华搭档超精彩！(25字) (Time: 2025-07-14 13:26:10)\",\n", "        \"timestamp\": \"2025-07-14 13:29:17\"\n", "    },\n", "    {\n", "        \"user_input\": \"我想摸鱼了\",\n", "        \"agent_response\": \"来点轻松的~偷偷刷会儿短视频，或者假装去接个\\\"重要电话\\\"？(25字)\",\n", "        \"timestamp\": \"2025-07-14 13:54:24\"\n", "    },\n", "    {\n", "        \"user_input\": \"我叫什么来着你还记得么？\",\n", "        \"agent_response\": \"当然记得啦吴可！我们第一次聊天你就告诉我啦~ (21字) (Time: 2025-07-14 14:00:00)\",\n", "        \"timestamp\": \"2025-07-14 13:57:45\"\n", "    }\n", "]"]}, {"cell_type": "code", "execution_count": 3, "id": "0d5d7ffc", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-23 10:07:46,261 - INFO - package: mysql.connector.plugins\n", "2025-07-23 10:07:46,262 - INFO - plugin_name: caching_sha2_password\n", "2025-07-23 10:07:46,263 - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin\n", "2025-07-23 10:07:46,279 - INFO - ✅ 成功连接到数据库\n", "2025-07-23 10:07:46,303 - ERROR - 表 short_term_record 不存在\n", "2025-07-23 10:07:46,308 - INFO - 🛠️ 正在执行建表语句: \n", "    CREATE TABLE IF NOT EXISTS short_term_record (\n", "        id INT PRIMARY KEY AUTO_INCREMENT,\n", "        user_id VARCHAR(50) NOT NULL,\n", "        user_input TEXT NOT NULL,\n", "        agent_response TEXT NOT NULL,\n", "        `timestamp` DATETIME NOT NULL,\n", "        INDEX idx_user_id (user_id),\n", "        INDEX idx_timestamp (`timestamp`)\n", "    )\n", "    \n", "2025-07-23 10:07:46,314 - INFO - 🆕 表 short_term_record 已创建或已存在\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["create_short_term_table(\"short_term_record\")"]}, {"cell_type": "code", "execution_count": 4, "id": "6f00202a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-23 10:08:12,842 - INFO - ✅ 成功连接到数据库\n", "2025-07-23 10:08:12,856 - INFO - ✅ 删除成功\n", "2025-07-23 10:08:12,903 - INFO - ✅ 插入记录成功!\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["save_short_term_to_mysql(user_id=\"user_1\", data=chat_data)"]}, {"cell_type": "code", "execution_count": 5, "id": "5f7c17a4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-23 10:08:27,213 - INFO - ✅ 成功连接到数据库\n", "2025-07-23 10:08:27,217 - INFO - ✅ 读取成功!\n"]}, {"data": {"text/plain": ["[{'user_input': '行，这懂 啊，我现在就去，去看看江苏足球联赛',\n", "  'agent_response': '哈哈，江苏队最近状态不错啊！记得带点零食去看球~(25字)',\n", "  'timestamp': '2025-07-14 13:23:52'},\n", " {'user_input': '上次说的吴京演的电影叫什么来着',\n", "  'agent_response': '吴京的《战狼2》超燃！还有《流浪地球》也很棒~(25字) (Time: 2025-07-14 13:24:30)',\n", "  'timestamp': '2025-07-14 13:24:55'},\n", " {'user_input': '你记吴京演出的电影叫什么',\n", "  'agent_response': '吴京主演的《长津湖》超震撼！还有《战狼》系列也超经典~(25字)',\n", "  'timestamp': '2025-07-14 13:25:33'},\n", " {'user_input': '这个不我们之前聊过的，我想问的是，之前我们聊过的吴京和刘德华一起演的电影名称',\n", "  'agent_response': '啊对！是《流浪地球2》，吴京和刘德华搭档超精彩！(25字) (Time: 2025-07-14 13:26:10)',\n", "  'timestamp': '2025-07-14 13:29:17'},\n", " {'user_input': '我想摸鱼了',\n", "  'agent_response': '来点轻松的~偷偷刷会儿短视频，或者假装去接个\"重要电话\"？(25字)',\n", "  'timestamp': '2025-07-14 13:54:24'},\n", " {'user_input': '我叫什么来着你还记得么？',\n", "  'agent_response': '当然记得啦吴可！我们第一次聊天你就告诉我啦~ (21字) (Time: 2025-07-14 14:00:00)',\n", "  'timestamp': '2025-07-14 13:57:45'}]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["load_short_term_from_mysql(user_id=\"user_1\")"]}], "metadata": {"kernelspec": {"display_name": "MACRec", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}