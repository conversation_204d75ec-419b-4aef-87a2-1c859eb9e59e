#---------------#
# 适配mid_term的操作
#---------------#

from pymilvus import MilvusClient, connections, Collection, FieldSchema, DataType, CollectionSchema
from config.database_config import MILVUS_URI, MILVUS_TOKEN

import sys
import os
import json
import logging


# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


#---------------------------------------------------------------------------------summaryEmbedding----------------------------------------------------------------------#

# 删除一个分区
def drop_collection(collection_name:str="mid_term_record") -> bool:
    """
    input:
        collection_name:待删除的collection的名称
    """
    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False
    # 执行删除操作
    try:
        milvus_client.drop_collection(
        collection_name=collection_name)
        logging.info(f"✅ Delete collection '{collection_name}' with schema.")
        return True
    except Exception as e:
        logging.info("⚠️ 数据库删除失败", e)
        return False



# 创建一个带分区的collection（不可复用）
def create_collection_with_partitions(collection_name:str="mid_term_record", dimension:int=1024) -> bool:
    """
    input:
        collection_name:collection的名称
        dimension:向量的维数
        local:是否是本地连接
    """

    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False

    # 如果已经有则不需要创建
    if milvus_client.has_collection(collection_name):
        print(f"Collection {collection_name} already exists.")
        return True
    
    try:
        # 使用 MilvusClient.create_schema() 创建 schema
        schema = milvus_client.create_schema(
            auto_id=False,
            description="Session Summary Embedding by User Partition"
        )

        # 添加字段
        schema.add_field(field_name="id", datatype=DataType.VARCHAR, is_primary=True, max_length=100)
        schema.add_field(field_name="session_id", datatype=DataType.VARCHAR, max_length=100)
        schema.add_field(field_name="summary_embedding", datatype=DataType.FLOAT_VECTOR, dim=dimension)
        schema.add_field(field_name="summary", datatype=DataType.VARCHAR, max_length=200, nullable=True)
        schema.add_field(field_name="summary_keywords", datatype=DataType.VARCHAR, max_length=100, nullable=True)
        schema.add_field(field_name="details", datatype=DataType.VARCHAR, max_length=100, nullable=True)
        schema.add_field(field_name="L_interaction", datatype=DataType.FLOAT, nullable=True)
        schema.add_field(field_name="R_recency", datatype=DataType.FLOAT, nullable=True)
        schema.add_field(field_name="N_visit", datatype=DataType.FLOAT, nullable=True)
        schema.add_field(field_name="H_segment", datatype=DataType.FLOAT, nullable=True)
        schema.add_field(field_name="timestamp", datatype=DataType.VARCHAR, max_length=100, nullable=True)
        schema.add_field(field_name="last_visit_time", datatype=DataType.VARCHAR, max_length=100, nullable=True)
        schema.add_field(field_name="access_count_lfu", datatype=DataType.FLOAT, nullable=True)
        schema.add_field(field_name="access_frequency", datatype=DataType.FLOAT, nullable=True)

        # 创建索引
        index_params = milvus_client.prepare_index_params(
            field_name="summary_embedding",
            index_type="IVF_FLAT",
            metric_type="L2",
            params={"nlist": 128}
        )

        # 创建 collection
        milvus_client.create_collection(collection_name=collection_name, schema=schema, index_params=index_params)

        logging.info(f"✅ Created collection '{collection_name}' with schema.")
        return True
    except Exception as e:
        logging.info("⚠️ 创建向量表失败！", e)
        return False
    

# 为用户单独创建一个分区(可复用)
def create_partition_if_not_exists(collection_name:str="mid_term_record", partition_name: str="default")->bool:
    """
    input:
        collection_name:表的名称
        partition_name:分区的名称（用户的id）
    """
    # 验证数据完整性
    if partition_name == "default":
        logging.info("⚠️ 没有给出用户id，将数据放置到默认分区!")

    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False

    # 如果没有表说明创建失败
    if not milvus_client.has_collection(collection_name):
        print(f"Collection {collection_name} already exists.")
        return False

    # 判断分区是否存在
    if milvus_client.has_partition(collection_name=collection_name, partition_name=partition_name):
        print(f"Partition '{partition_name}' already exists.")
        return True

    try:
        milvus_client.create_partition(collection_name=collection_name, partition_name=partition_name)
        logging.info(f"✅ Created partition '{partition_name}'")
        return True
    except Exception as e:
        logging.info(f"❌ Created partition fail '{partition_name}'")
        return False

# 插入每个session_id对应的summary_embedding，我们认为要传入的data就是原始的sessions(不要变动)（不可复用）
def insert_session_embeddings_by_user(user_id:str="default", data: dict=None, collection_name:str="mid_term_record" ,dimesion:int=1024)->bool:
    """
    user_id: 用户的id，用来搜索对应的分区
    data: JSON 格式的原始数据
    collection_name:集合名称
    dimesion:向量维度（用来做合法性判断）
    """
    # 验证数据完整性
    if data == None:
        logging.info("⚠️ 没有给出要插入的数据!")
        return False
    
    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False
    
    # 判断分区是否存在
    if not milvus_client.has_partition(collection_name=collection_name, partition_name=user_id):
        logging.info("⚠️ 分区不存在，请创建分区!")
        if not create_partition_if_not_exists(collection_name=collection_name, partition_name=user_id):
            return False
    
    # 按照输入数据预定的格式来抽取信息
    try:
        sessions = data.get("sessions", {})
        access_frequencys = data.get("access_frequency", {})
        
        # 将所有数据存入
        for session_id, session_data in sessions.items():
            summary = session_data.get("summary", "")
            summary_keywords = str(session_data.get("summary_keywords", []))
            details = user_id+session_id
            L_interaction = session_data.get("L_interaction", 0.0)
            R_recency = session_data.get("R_recency", 0.0)
            N_visit = session_data.get("N_visit", 0.0)
            H_segment = session_data.get("H_segment", 1.0)
            timestamp = session_data.get("timestamp", "")
            last_visit_time = session_data.get("last_visit_time", "")
            access_count_lfu = session_data.get("access_count_lfu", 0.0)
            access_frequency = access_frequencys.get(session_id, 0.0)
            embedding = session_data.get("summary_embedding", [])
            if not embedding or len(embedding) != dimesion:
                logging.info(f"❌ Invalid embedding for {session_id}, skipping...")
                continue

            # 构造插入数据
            insert_data = {
                "id": session_id,
                "session_id": session_id,
                "summary_embedding": embedding,
                "summary": summary,
                "summary_keywords": summary_keywords,
                "details": details,
                "L_interaction": L_interaction,
                "R_recency": R_recency,
                "N_visit": N_visit,
                "H_segment": H_segment,
                "timestamp": timestamp,
                "last_visit_time": last_visit_time,
                "access_count_lfu": access_count_lfu,
                "access_frequency": access_frequency,
            }

            # 检查是否已存在该 session_id
            res = milvus_client.query(
                collection_name=collection_name,
                filter=f"id == '{session_id}'",
                output_fields=["id"],
                partition_names=[user_id]
            )

            if res:
                # 存在 -> 删除再插入（模拟更新）
                milvus_client.delete(collection_name=collection_name, filter=f"id == '{session_id}'", partition_name=user_id)
                logging.info(f"🔄 Updated session '{session_id}' in partition '{user_id}'")

            # 插入新数据（无论是否存在）
            milvus_client.insert(collection_name=collection_name, data=[insert_data], partition_name=user_id)
            logging.info(f"📌 Inserted session '{session_id}' into partition '{user_id}'")
        return True
    except Exception as e:
        logging.info("❌ 插入新数据失败，请重新尝试!", e)
        return False
    
# 删除指定session_id对应的summary_embedding（不可复用）
def delete_session_embeddings_by_user(user_id:str="default", session_ids: list[str]=None, collection_name:str="mid_term_record", clear_all:bool=False)->bool:
    """
    user_id: 用户的id，用来搜索对应的分区
    data: JSON 格式的原始数据
    clear_all:删除所有数据
    """
    # 验证数据完整性
    if session_ids == None:
        logging.info("⚠️ 没有给出session_id无法定位删除!")
        return False
    
    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False
    
    # 判断分区是否存在
    if not milvus_client.has_partition(collection_name=collection_name, partition_name=user_id):
        logging.info("⚠️ 分区不存在，不存在要删除的数据!")
        return True
    
    # 删除所有数据
    if clear_all:
        try:
            milvus_client.delete(collection_name=collection_name, partition_name=user_id, filter="id != ''")
            milvus_client.flush(collection_name=collection_name)
            logging.info("📌 删除所有数据!")
            return True
        except Exception as e:
            logging.info("❌ 删除所有数据失败，请重新尝试!", e)
            return False

    # 按照输入数据预定的格式来抽取信息
    try:
        for session_id in session_ids:
            milvus_client.delete(collection_name=collection_name, filter=f"id == '{session_id}'", partition_name=user_id)
            logging.info(f"🔄 Deleted session '{session_id}' in partition '{user_id}'")
        return True
    except Exception as e:
        logging.info("❌ 删除指定数据失败，请重新尝试!", e)
        return False

# 检索需要的session_id（不可复用）
def retrieve_session_embeddings_by_user(user_id:str="default", session_ids: list=[], collection_name:str="mid_term_record")->dict:
    """
    client: MilvusClient 实例
    user_id: 用户 ID，用作分区名
    session_ids: 要查询的 session_id 列表
    return: 
        查询结果字典 {session_id: embedding}
    """
    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False

    if not milvus_client.has_partition(collection_name=collection_name, partition_name=user_id):
        print(f"❌ Partition '{user_id}' does not exist.")
        return {}

    results = {}
    try:
        for session_id in session_ids:
            res = milvus_client.query(
                collection_name=collection_name,
                filter=f"id == '{session_id}'",
                output_fields=["summary_embedding"],
                partition_names=[user_id]
            )
            if res:
                results[session_id] = res[0]["summary_embedding"]
            else:
                results[session_id] = None
        return results
    except Exception as e:
        logging.info("❌ 查询数据失败，请重新尝试!", e)
        return {}

# 基于向量的检索（不可复用）
def search_similar_sessions(user_id: str="default", query_embedding: list=[], top_k: int = 5, collection_name:str="mid_term_record")->list:
    """
    user_id: 用户 ID，即分区名
    query_embedding: 查询向量，例如 [0.1, 0.2, ..., 0.768]
    top_k: 返回最相似的 top-k 个结果
    :return: 列表，每个元素是 (session_id, distance) 元组
    """

    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return []

    # 获取集合描述信息
    collection_info = milvus_client.describe_collection(collection_name=collection_name)

    vector_field_name = "summary_embedding"
    button = True
    # 查找特定向量字段
    for field in collection_info['fields']:
        if field['name'] == vector_field_name:
            button = False
            if field['type'] == DataType.FLOAT_VECTOR or field['type'] == DataType.BINARY_VECTOR:
                vector_dimension = field['params']['dim']
                if vector_dimension != len(query_embedding):
                    logging.info(f"向量维度不匹配！ '{vector_field_name}' is: {vector_dimension}")
                    return []
    if button:
        logging.info(f"Vector field '{vector_field_name}' not found.")
        return []
    
    if not milvus_client.has_collection(collection_name):
        logging.info(f"❌ Collection '{collection_name}' 不存在.")
        return []

    if not milvus_client.has_partition(collection_name=collection_name, partition_name=user_id):
        print(f"❌ Partition '{user_id}' 不存在.")
        return []

    try:
        # 执行搜索
        search_result = milvus_client.search(
            collection_name=collection_name,
            data=[query_embedding],  # 要搜索的向量列表（支持批量）
            limit=top_k,
            filter="",  # 可选过滤条件
            output_fields=["session_id",
                           "summary",
                           "summary_keywords",
                           "summary_embedding",
                           "details",
                           "L_interaction",
                           "R_recency",
                           "N_visit",
                           "H_segment",
                           "timestamp",
                           "last_visit_time",
                           "access_count_lfu",
                           "access_frequency"],  # 需要返回的字段
            partition_names=[user_id]  # 指定搜索分区
        )

        # 解析结果
        results = []
        for hit in search_result[0]:  # 第一个查询向量的结果
            session_id = hit.get("entity", {}).get("session_id", "")
            summary = hit.get("entity", {}).get("summary", "")
            summary_keywords = hit.get("entity", {}).get("summary_keywords", "")
            summary_embedding = hit.get("entity", {}).get("summary_embedding", [])
            details = hit.get("entity", {}).get("details", "")
            L_interaction = hit.get("entity", {}).get("L_interaction", 0.0)
            R_recency = hit.get("entity", {}).get("R_recency", 0.0)
            N_visit = hit.get("entity", {}).get("N_visit", 0.0)
            H_segment = hit.get("entity", {}).get("H_segment", 0.0)
            timestamp = hit.get("entity", {}).get("timestamp", "")
            last_visit_time = hit.get("entity", {}).get("last_visit_time", "")
            access_count_lfu = hit.get("entity", {}).get("access_count_lfu", 0.0)
            access_frequency = hit.get("entity", {}).get("access_frequency", 0.0)
            distance = hit.get("distance")
            if session_id:
                # 构造插入数据
                output_data = {
                    "output": {
                        "id": session_id,
                        "session_id": session_id,
                        "summary": summary,
                        "summary_keywords": summary_keywords,
                        "summary_embedding": summary_embedding,
                        "details": details,
                        "L_interaction": L_interaction,
                        "R_recency": R_recency,
                        "N_visit": N_visit,
                        "H_segment": H_segment,
                        "timestamp": timestamp,
                        "last_visit_time": last_visit_time,
                        "access_count_lfu": access_count_lfu,
                        "access_frequency": access_frequency,
                    },
                    "distance": distance,
                }
                results.append(output_data)

        print(f"🔍 Found top {len(results)} similar sessions in partition '{user_id}':")

        return results
    except Exception as e:
        logging.info("⚠️ 查询失败", e)
        return []

#---------------------------------------------------------------------------------pageEmbedding----------------------------------------------------------------------#

# 创建一个带分区的collection,给page用的
def create_collection_page_with_partitions(collection_name:str="mid_term_record_page", dimension:int=1024) -> bool:
    """
    input:
        collection_name:collection的名称
        dimension:向量的维数
        local:是否是本地连接
    """

    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False

    # 如果已经有则不需要创建
    if milvus_client.has_collection(collection_name):
        print(f"Collection {collection_name} already exists.")
        return True
    
    try:
        # 使用 MilvusClient.create_schema() 创建 schema
        schema = milvus_client.create_schema(
            auto_id=False,
            description="Session Page Embedding by User Partition"
        )

        # 添加字段
        schema.add_field(field_name="id", datatype=DataType.VARCHAR, is_primary=True, max_length=100)  # page_id
        schema.add_field(field_name="user_input", datatype=DataType.VARCHAR, max_length=8192, nullable=True)
        schema.add_field(field_name="agent_response", datatype=DataType.VARCHAR, max_length=8192, nullable=True)
        schema.add_field(field_name="timestamp", datatype=DataType.VARCHAR, max_length=100, nullable=True)
        schema.add_field(field_name="preloaded", datatype=DataType.BOOL, nullable=True)
        schema.add_field(field_name="analyzed", datatype=DataType.BOOL, nullable=True)
        schema.add_field(field_name="pre_page", datatype=DataType.VARCHAR, max_length=100, nullable=True)
        schema.add_field(field_name="next_page", datatype=DataType.VARCHAR, max_length=100, nullable=True)
        schema.add_field(field_name="meta_info", datatype=DataType.VARCHAR, max_length=200, nullable=True)
        schema.add_field(field_name="page_keywords", datatype=DataType.VARCHAR, max_length=100, nullable=True)
        schema.add_field(field_name="page_embedding", datatype=DataType.FLOAT_VECTOR, dim=dimension)

        # 创建索引
        index_params = milvus_client.prepare_index_params(
            field_name="page_embedding",
            index_type="IVF_FLAT",
            metric_type="L2",
            params={"nlist": 128}
        )

        # 创建 collection
        milvus_client.create_collection(collection_name=collection_name, schema=schema, index_params=index_params)

        logging.info(f"✅ Created collection '{collection_name}' with schema.")
        return True
    except Exception as e:
        logging.info("⚠️ 创建向量表失败！", e)
        return False
    

# 插入每个page_id对应的page_embedding，我们认为要传入的data就是原始的sessions(不要变动)（不可复用）
def insert_page_embeddings_by_user(user_id:str="default", data: dict=None, collection_name:str="mid_term_record_page" ,dimesion:int=1024)->bool:
    """
    user_id: 用户的id，用来搜索对应的分区
    data: JSON 格式的原始数据
    collection_name:集合名称
    dimesion:向量维度（用来做合法性判断）
    """
    # 验证数据完整性
    if data == None:
        logging.info("⚠️ 没有给出要插入的数据!")
        return False
    
    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False

    # 按照输入数据预定的格式来抽取信息
    try:
        sessions = data.get("sessions", {})
        for session_id, session_data in sessions.items():
            details = session_data.get("details", [])

            # 判断分区是否存在
            if not milvus_client.has_partition(collection_name=collection_name, partition_name=user_id+session_id):
                logging.info("⚠️ 分区不存在，请创建分区!")
                if not create_partition_if_not_exists(collection_name=collection_name, partition_name=user_id+session_id):
                    return False
            
            for detail in details:
                page_id = detail.get("page_id", "default")
                user_input = detail.get("user_input", "")
                agent_response = detail.get("agent_response", "")
                timestamp = detail.get("timestamp", "")
                preloaded = detail.get("preloaded", False)
                analyzed = detail.get("analyzed", False)
                pre_page = str(detail.get("pre_page", "default"))
                next_page = str(detail.get("next_page", "default"))
                meta_info = detail.get("meta_info", "")
                page_keywords = str(detail.get("page_keywords", []))
                embedding = detail.get("page_embedding", [])
                if not embedding or len(embedding) != dimesion:
                    logging.info(f"❌ Invalid embedding for {page_id}, skipping...")
                    continue

                # 构造插入数据
                insert_data = {
                    "id": page_id,
                    "user_input": user_input,
                    "agent_response": agent_response,
                    "timestamp": timestamp,
                    "preloaded": preloaded,
                    "analyzed": analyzed,
                    "pre_page": pre_page,
                    "next_page": next_page,
                    "meta_info": meta_info,
                    "page_keywords": page_keywords,
                    "page_embedding": embedding
                }

                # 检查是否已存在该 page_id
                res = milvus_client.query(
                    collection_name=collection_name,
                    filter=f"id == '{page_id}'",
                    output_fields=["id"],
                    partition_names=[user_id+session_id]
                )

                if res:
                    # 存在 -> 删除再插入（模拟更新）
                    milvus_client.delete(collection_name=collection_name, filter=f"id == '{page_id}'", partition_name=user_id+session_id)
                    logging.info(f"🔄 Updated session '{page_id}' in partition '{user_id+session_id}'")

                # 插入新数据（无论是否存在）
                milvus_client.insert(collection_name=collection_name, data=[insert_data], partition_name=user_id+session_id)
                logging.info(f"📌 Inserted session '{page_id}' into partition '{user_id+session_id}'")
        return True
    except Exception as e:
        logging.info("❌ 插入新数据失败，请重新尝试!", e)
        return False
    
# 删除指定user_id+session_id对应的page_embedding（不可复用）
def delete_page_embeddings_by_user(user_id:str="default", session_id:str="session_default",page_ids:list[str]=None, collection_name:str="mid_term_record_page", clear_all:bool=False)->bool:
    """
    user_id: 用户的id，用来搜索对应的分区
    data: JSON 格式的原始数据
    clear_all:删除所有数据
    """
    # 验证数据完整性
    if page_ids == None:
        logging.info("⚠️ 没有给出pages_id无法定位删除!")
        return False
    
    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False
    
    # 判断分区是否存在
    if not milvus_client.has_partition(collection_name=collection_name, partition_name=user_id+session_id):
        logging.info("⚠️ 分区不存在，不存在要删除的数据!")
        return True
    
    # 删除所有数据
    if clear_all:
        try:
            milvus_client.delete(collection_name=collection_name, partition_name=user_id+session_id, filter="id != ''")
            milvus_client.flush(collection_name=collection_name)
            logging.info("📌 删除所有数据!")
            return True
        except Exception as e:
            logging.info("❌ 删除所有数据失败，请重新尝试!", e)
            return False

    # 按照输入数据预定的格式来抽取信息
    try:
        for page_id in page_ids:
            milvus_client.delete(collection_name=collection_name, filter=f"id == '{page_id}'", partition_name=user_id+session_id)
            logging.info(f"🔄 Deleted session '{page_ids}' in partition '{user_id+session_id}'")
        return True
    except Exception as e:
        logging.info("❌ 删除指定数据失败，请重新尝试!", e)
        return False

# 基于向量的检索（不可复用）
def search_similar_pages(user_id: str="default", session_id:str="session_default" ,query_embedding: list=[], top_k: int = 5, collection_name:str="mid_term_record_page")->list:
    """
    user_id: 用户 ID，即分区名
    query_embedding: 查询向量，例如 [0.1, 0.2, ..., 0.768]
    top_k: 返回最相似的 top-k 个结果
    return: 列表，每个元素是 (session_id, distance) 元组
    """

    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return []

    # 获取集合描述信息
    collection_info = milvus_client.describe_collection(collection_name=collection_name)

    vector_field_name = "page_embedding"
    button = True
    # 查找特定向量字段
    for field in collection_info['fields']:
        if field['name'] == vector_field_name:
            button = False
            if field['type'] == DataType.FLOAT_VECTOR or field['type'] == DataType.BINARY_VECTOR:
                vector_dimension = field['params']['dim']
                if vector_dimension != len(query_embedding):
                    logging.info(f"向量维度不匹配！ '{vector_field_name}' is: {vector_dimension}")
                    return []
    if button:
        logging.info(f"Vector field '{vector_field_name}' not found.")
        return []
    
    if not milvus_client.has_collection(collection_name):
        logging.info(f"❌ Collection '{collection_name}' 不存在.")
        return []

    if not milvus_client.has_partition(collection_name=collection_name, partition_name=user_id+session_id):
        print(f"❌ Partition '{user_id+session_id}' 不存在.")
        return []

    try:
        # 执行搜索
        search_result = milvus_client.search(
            collection_name=collection_name,
            data=[query_embedding],  # 要搜索的向量列表（支持批量）
            limit=top_k,
            filter="",  # 可选过滤条件
            output_fields=["id"],  # 需要返回的字段
            partition_names=[user_id+session_id]  # 指定搜索分区
        )

        # 解析结果
        results = []
        for hit in search_result[0]:
            # 第一个查询向量的结果
            page_id = hit.get("entity", {}).get("id", "default")
            user_input = hit.get("entity", {}).get("user_input", "")
            agent_response = hit.get("entity", {}).get("agent_response", "")
            timestamp = hit.get("entity", {}).get("timestamp", "")
            preloaded = hit.get("entity", {}).get("preloaded", False)
            analyzed = hit.get("entity", {}).get("analyzed", False)
            pre_page = hit.get("entity", {}).get("pre_page", "default")
            next_page = hit.get("entity", {}).get("next_page", "default")
            meta_info = hit.get("entity", {}).get("meta_info", "")
            embedding = hit.get("entity", {}).get("page_embedding", [])
            distance = hit.get("distance")
            if page_id:
                # 构造插入数据
                output_data = {
                    "output": {
                        "id": page_id,
                        "user_input": user_input,
                        "agent_response": agent_response,
                        "timestamp": timestamp,
                        "preloaded": preloaded,
                        "analyzed": analyzed,
                        "pre_page": pre_page,
                        "next_page": next_page,
                        "meta_info": meta_info,
                        "page_embedding": embedding
                    },
                    "distance": distance,
                }
                results.append(output_data)

        print(f"🔍 Found top {len(results)} similar sessions in partition '{user_id+session_id}':")
        return results
    except Exception as e:
        logging.info("⚠️ 查询失败", e)
        return []


#---------------------------------------------------------------------------------LongtermEmbedding----------------------------------------------------------------------#
# 创建一个长期记忆带分区的collection（不可复用）
def create_long_collection_with_partitions(collection_name:str="long_term_record", dimension:int=1024) -> bool:
    """
    input:
        collection_name:collection的名称
        dimension:向量的维数
        local:是否是本地连接
    """

    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False

    # 如果已经有则不需要创建
    if milvus_client.has_collection(collection_name):
        logging.info(f"Collection {collection_name} already exists.")
        return True
    
    try:
        # 使用 MilvusClient.create_schema() 创建 schema
        schema = milvus_client.create_schema(
            auto_id=False,
            description="Session Summary Embedding by User Partition"
        )

        # 添加字段
        schema.add_field(field_name="id", datatype=DataType.VARCHAR, is_primary=True, max_length=100)
        schema.add_field(field_name="embedding", datatype=DataType.FLOAT_VECTOR, dim=dimension)
        schema.add_field(field_name="data", datatype=DataType.VARCHAR, max_length=8192, nullable=True)
        schema.add_field(field_name="last_updated", datatype=DataType.VARCHAR, max_length=100, nullable=True)

        # 创建索引
        index_params = milvus_client.prepare_index_params(
            field_name="embedding",
            index_type="IVF_FLAT",
            metric_type="L2",
            params={"nlist": 128}
        )

        index_params.add_index(
            field_name="id",
            index_type="AUTOINDEX"
        )
        

        # 创建 collection
        milvus_client.create_collection(collection_name=collection_name, schema=schema, index_params=index_params)

        logging.info(f"✅ Created collection '{collection_name}' with schema.")
        return True
    except Exception as e:
        logging.info("⚠️ 创建向量表失败！", e)
        return False


# 创建一个长期记忆带分区的collection（不可复用）
def create_long_knowledge_collection_with_partitions(collection_name:str="long_term_usr_knowledge_record", dimension:int=1024) -> bool:
    """
    input:
        collection_name:collection的名称
        dimension:向量的维数
        local:是否是本地连接
    """

    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False

    # 如果已经有则不需要创建
    if milvus_client.has_collection(collection_name):
        print(f"Collection {collection_name} already exists.")
        return True
    
    try:
        # 使用 MilvusClient.create_schema() 创建 schema
        schema = milvus_client.create_schema(
            auto_id=True,
            description="Session Summary Embedding by User Partition"
        )

        # 添加字段
        schema.add_field(field_name="id", datatype=DataType.VARCHAR, is_primary=True, max_length=100)
        schema.add_field(field_name="knowledge_embedding", datatype=DataType.FLOAT_VECTOR, dim=dimension)
        schema.add_field(field_name="knowledge", datatype=DataType.VARCHAR, max_length=8192, nullable=True)
        schema.add_field(field_name="timestamp", datatype=DataType.VARCHAR, max_length=100, nullable=True)

        # 创建索引
        index_params = milvus_client.prepare_index_params(
            field_name="knowledge_embedding",
            index_type="IVF_FLAT",
            metric_type="L2",
            params={"nlist": 128}
        )

        # 创建 collection
        milvus_client.create_collection(collection_name=collection_name, schema=schema, index_params=index_params)

        logging.info(f"✅ Created collection '{collection_name}' with schema.")
        return True
    except Exception as e:
        logging.info("⚠️ 创建向量表失败！", e)
        return False

# 插入长期记忆，因为长期记忆比较特殊，输入原始数据格式，一次性我们要更改三个表，并且需要输入用户id做个匹配（不可复用）
def insert_long_term_by_user(data: dict=None,
                             collection_name:str="long_term_record",
                             user_knowledge_name="long_term_usr_knowledge_record",
                             assistant_lnowledge_name="long_term_assistant_knowledge_record",
                             dimesion:int=1024)->bool:
    """
    data: JSON 格式的原始数据
    collection_name:集合名称
    user_knowledge_name:用户的知识库
    assistant_lnowledge_name:助手的知识库
    dimesion:向量维度（用来做合法性判断）
    """
    # 验证数据完整性
    if data == None:
        logging.info("⚠️ 没有给出要插入的数据!")
        return False
    
    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False

    user_id = ""
    # 匹配用户id是否一致，并传入第一个表的信息
    try:
        user_profiles = data.get("user_profiles", {})
        for user_new_id, user_value in user_profiles.items():
            user_id = user_new_id
            # 读取失败的策略
            if user_id == "":
                logging.info("⚠️ 读取用户名称失败")
                return False
            user_data = user_value.get("data", "")
            last_updated = user_value.get("last_updated", "")

            # 构造插入数据
            insert_user_data = {
                "id": user_id,
                "embedding": [0]*dimesion,
                "data": user_data,
                "last_updated": last_updated,
            }

            # 检查是否已存在该 session_id
            res = milvus_client.query(
                collection_name=collection_name,
                filter=f"id == '{user_id}'",
                output_fields=["id"],
            )
            if res:
                milvus_client.delete(collection_name=collection_name, filter=f"id == '{user_id}'")
                logging.info(f"🔄 Updated user: '{user_id}'")

            milvus_client.insert(collection_name=collection_name, data=[insert_user_data])
            logging.info(f"📌 Inserted user '{user_id}'")
    except Exception as e:
        logging.info("❌ 插入用户信息失败，请重新尝试!", e)
        return False

    # 按照输入数据预定的格式来抽取信息
    try:
        for knowledge_name in ["knowledge_base", "assistant_knowledge"]:
            # 确定数据库表的名称
            knowledge_collection_name = ""
            if knowledge_name == "knowledge_base":
                knowledge_collection_name = user_knowledge_name
            elif knowledge_name == "assistant_knowledge":
                knowledge_collection_name = assistant_lnowledge_name
            else:
                logging.info(f"⚠️ 中途出现了问题！")
                return False
            
            # 如果没有分区就创建分区
            if not milvus_client.has_partition(collection_name=knowledge_collection_name, partition_name=user_id):
                logging.info("⚠️ 分区不存在，请创建分区!")
                if not create_partition_if_not_exists(collection_name=knowledge_collection_name, partition_name=user_id):
                    return False
            
            # 提取数据 ***********!
            knowledge_list = data.get(knowledge_name, [])
            for value in knowledge_list:
                knowledge = value.get("knowledge", "")
                timestamp = value.get("timestamp", "")
                knowledge_embedding = value.get("knowledge_embedding", [])

                # 构造插入数据
                insert_knowledge_data = {
                    "knowledge": knowledge,
                    "timestamp": timestamp,
                    "knowledge_embedding": [0.76]*dimesion,
                }

                # 检查是否已存在该id
                res = milvus_client.query(
                    collection_name=knowledge_collection_name,
                    filter=f"knowledge == '{knowledge}'",
                    output_fields=["knowledge"],
                    partition_names=[user_id]
                )
                if res:
                    milvus_client.delete(collection_name=knowledge_collection_name, filter=f"knowledge == '{knowledge}'", partition_name=user_id)
                    logging.info(f"🔄 Updated knowledge:{knowledge_collection_name}")

                milvus_client.insert(collection_name=knowledge_collection_name, data=[insert_knowledge_data], partition_name=user_id)
                logging.info(f"📌 Inserted knowledge:{knowledge_collection_name}")
        return True
    except Exception as e:
        logging.info("❌ 插入新数据失败，请重新尝试!", e)
        return False

# 检索需要的session_id（不可复用）
def retrieve_longterm_by_user(user_id:str="default", collection_name:str="long_term_record")->dict:
    """
    client: MilvusClient 实例
    user_id: 用户 ID，用作分区名
    session_ids: 要查询的 session_id 列表
    return: 
        查询结果字典 {session_id: embedding}
    """
    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return False
    
    results = {}
    try:
        res = milvus_client.query(
            collection_name=collection_name,
            filter=f"id == '{user_id}'",
            output_fields=["data", "last_updated"],
        )
        if res:
            results[user_id] = {
                "data": res[0]["data"],
                "last_updated": res[0]["last_updated"]
            }
            return results
        else:
            return {}
    except Exception as e:
        logging.info("❌ 查询数据失败，请重新尝试!", e)
        return {}


# 基于向量的检索（不可复用）
def search_long_term_knowledges(user_id: str="default", query_embedding: list=[], top_k: int = 5, collection_name:str="long_term_usr_knowledge_record")->list:
    """
    user_id: 用户 ID，即分区名
    query_embedding: 查询向量，例如 [0.1, 0.2, ..., 0.768]
    top_k: 返回最相似的 top-k 个结果
    return: 列表，每个元素是 (session_id, distance) 元组
    """

    # 创建连接库
    try:
        if "localhost" in MILVUS_URI:
            milvus_client = MilvusClient(uri=MILVUS_URI)
        else:
            milvus_client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)
    except Exception as e:
        logging.info("⚠️ 数据库连接失败", e)
        return []

    # 获取集合描述信息
    collection_info = milvus_client.describe_collection(collection_name=collection_name)

    vector_field_name = "knowledge_embedding"
    button = True
    # 查找特定向量字段
    for field in collection_info['fields']:
        if field['name'] == vector_field_name:
            button = False
            if field['type'] == DataType.FLOAT_VECTOR or field['type'] == DataType.BINARY_VECTOR:
                vector_dimension = field['params']['dim']
                if vector_dimension != len(query_embedding):
                    logging.info(f"向量维度不匹配！ '{vector_field_name}' is: {vector_dimension}")
                    return []
    if button:
        logging.info(f"Vector field '{vector_field_name}' not found.")
        return []
    
    if not milvus_client.has_collection(collection_name):
        logging.info(f"❌ Collection '{collection_name}' 不存在.")
        return []

    if not milvus_client.has_partition(collection_name=collection_name, partition_name=user_id):
        logging.info(f"❌ Partition '{user_id}' 不存在.")
        return []

    try:
        # 执行搜索
        search_result = milvus_client.search(
            collection_name=collection_name,
            data=[query_embedding],  # 要搜索的向量列表（支持批量）
            limit=top_k,
            filter="",  # 可选过滤条件
            output_fields=["knowledge", "timestamp", "knowledge_embedding"],  # 需要返回的字段
            partition_names=[user_id]  # 指定搜索分区
        )

        # 解析结果
        results = []
        for hit in search_result[0]:
            # 第一个查询向量的结果
            knowledge = hit.get("entity", {}).get("knowledge", "")
            timestamp = hit.get("entity", {}).get("timestamp", "")
            embedding = hit.get("entity", {}).get("knowledge_embedding", [])
            distance = hit.get("distance")
            if knowledge:
                # 构造插入数据
                output_data = {
                    "output": {
                        "knowledge": knowledge,
                        "timestamp": timestamp,
                        "knowledge_embedding": embedding,
                    },
                    "distance": distance
                }
                results.append(output_data)

        print(f"🔍 Found top {len(results)} similar sessions in partition '{user_id}':")
        return results
    except Exception as e:
        logging.info("⚠️ 查询失败", e)
        return []

